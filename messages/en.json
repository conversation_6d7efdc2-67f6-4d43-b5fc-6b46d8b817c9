{"common": {"actions": {"actions": "Actions", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "update": "Update", "add": "Add", "create": "Create", "save": "Save", "search": "Search", "filter": "Filter", "verify": "Verify", "upload": "Upload", "save_as_draft": "Save as Draft", "next": "Next", "previous": "Previous", "publish": "Publish"}, "responses": {"yes": "Yes", "no": "No", "no_data": "No data available"}, "requirements": {"required": "Required", "optional": "Optional"}, "states": {"loading": "Loading...", "active": "Active", "inactive": "Inactive", "verified": "Verified", "pending": "Pending", "pending_verification": "Pending Verification"}, "status": {"error": "Error", "no_permission": "You do not have permission to access this resource."}, "fields": {"name": "Name", "description": "Description", "created_at": "Created At", "updated_at": "Updated At", "actions": "Actions", "id": "ID", "phone": "Phone", "email": "Email", "role": "Role", "status": "Status", "last_login": "Last Login", "address": "Address", "website": "Website", "founded_year": "Founded Year", "industry": "Industry", "contact": "Contact", "user": "User", "title": "Title", "company": "Company", "branch": "Branch", "location": "Location", "dateOfBirth": "Date of Birth", "gender": "Gender"}, "placeholders": {"please_input": "Please input", "please_select": "Please select an option", "please_enter_valid": "Please enter a valid", "search_placeholder": "Search", "select_salary_period": "Select salary period"}, "messages": {"success": {"deleted_successfully": "deleted successfully", "updated_successfully": "updated successfully", "created_successfully": "created successfully", "verified_successfully": "verified successfully", "uploaded_successfully": "uploaded successfully", "post_updated": "Post updated successfully", "post_created": "Post created successfully"}, "error": {"failed_to_delete": "Failed to delete", "failed_to_update": "Failed to update", "failed_to_create": "Failed to create", "failed_to_verify": "Failed to verify", "upload_failed": "Upload failed", "failed_to_load_post_data": "Failed to load post data. Please try again.", "failed_to_save_post": "Failed to save post. Please check your inputs and try again."}, "confirm_delete": "Confirm Delete", "delete_confirmation_message": "Are you sure you want to delete this item?", "delete_confirmation_warning": "This action cannot be undone."}, "roles": {"employer": "Employer", "job_seeker": "<PERSON>"}, "file_types": {"image": "Image", "document": "Document", "logo": "Logo"}}, "constants": {"job": {"types": {"part_time": "Part Time", "full_time": "Full Time"}, "work_types": {"on_site": "On-site", "remote": "Remote", "hybrid": "Hybrid"}, "experience_levels": {"no_requirement": "No Experience Required", "under_1_year": "Under 1 Year", "1_to_2_years": "1-2 Years", "2_to_5_years": "2-5 Years", "over_5_years": "Over 5 Years"}, "salary_periods": {"hourly": "Hourly", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly"}, "salary_currencies": {"vnd": "VND", "usd": "USD", "eur": "EUR", "gbp": "GBP", "cny": "CNY"}}}, "form": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "password": "Password must be at least 6 characters long", "confirm_password": "Passwords do not match", "default_error": "An error occurred. Please try again.", "url": "Please enter a valid URL", "year": "Please enter a valid year", "debounce_select": {"default_helper_text": "Type to search"}}, "route": {"home": "Home", "dashboard": "Dashboard", "account_management": "Account Management", "users": "Users", "permissions": "Permissions", "roles": "Roles", "job_seekers_management": "Job Seekers Management", "employers_management": "Employers Management", "employers_list": "List", "transactions_management": "Transactions Management", "posts_management": "Posts Management", "transactions": "Transactions", "companies": "Companies", "contracts": "Contracts", "interviews": "Interviews", "user_management": "User Management", "job_seekers": "Job Seekers", "employers": "Employers", "message_management": "Message Management", "notifications": "Notifications", "campaigns": "Campaigns", "message_templates": "Message Templates", "sent_notification": "Sent Notifications", "new": "New", "edit": "Edit", "posts_employer": "Posts", "posts_job_seeker": "Posts", "resumes": "Resumes", "job_seekers_list": "List", "profile": "Profile", "wallet_management": "Wallet Management", "wallet_job_seeker": "Job Seeker Wallets", "wallet_employer": "Employer Wallets"}, "user": {"user_list": "User List", "email_verified": "<PERSON><PERSON>", "phone_verified": "Phone Verified", "name_filter": "Name", "role_filter": "Role", "status_filter": "Status", "admin": "Admin", "user": "User", "user_type": "User Type", "admin_portal_access": "Admin Portal Access", "password_cannot_be_changed": "Password cannot be changed", "user_deleted_successfully": "User {name} deleted successfully", "user_updated_successfully": "User updated successfully", "user_created_successfully": "User created successfully", "failed_to_delete_user": "Failed to delete user: {error}", "add_user": "Add User", "edit_user": "Edit User", "please_input_user_name": "Please input user name!", "please_input_phone_number": "Please input phone number!", "please_input_email": "Please input email!", "please_enter_valid_email": "Please enter a valid email!", "please_select_user_role": "Please select user role!", "failed_to_update_user": "Failed to update user: {error}"}, "messages": {"success": {"post_updated": "Post updated successfully", "post_created": "Post created successfully"}, "errors": {"failed_to_load_post_data": "Failed to load post data. Please try again.", "failed_to_save_post": "Failed to save post. Please check your inputs and try again."}}, "permissions": {"title": "Permissions", "add_permission": "Add Permission", "edit_permission": "Edit Permission", "delete_permission": "Delete Permission", "search_placeholder": "Search Permission", "permission_updated_successfully": "Permission updated successfully", "permission_created_successfully": "Permission created successfully", "permission_deleted_successfully": "Permission {name} deleted successfully", "failed_to_delete_permission": "Failed to delete permission: {error}", "permission_name": "Permission Name", "enter_permission_name": "Enter permission name", "enter_permission_name_subtitle": "Format: action_module (e.g., create_user, view_post)", "permission_description_placeholder": "Describe what this permission allows"}, "posts": {"post_management": "Post Management", "create_new_post": "Create New Post", "search_posts_placeholder": "Search posts by title, company...", "title": "Title", "featured": "Featured", "urgent": "<PERSON><PERSON>", "company": "Company", "location": "Location", "job_type": "Job Type", "applications": "Applications", "new": "new", "post_date": "Post Date", "expire_date": "Expire Date", "work_type": "Work Type", "work_schedule": {"other_periods": "Working Days & Hours", "start": "Start", "to": "to", "end": "End", "apply_to_all": "Apply to all active days", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "day_abbr": {"monday": "M", "tuesday": "T", "wednesday": "W", "thursday": "T", "friday": "F", "saturday": "S", "sunday": "S"}}, "post_date_range": "Post Date Range", "status_pending": "Pending", "status_active": "Active", "status_draft": "Draft", "status_closed": "Closed", "status_expired": "Expired", "work_type_onsite": "On-site", "work_type_remote": "Remote", "work_type_hybrid": "Hybrid", "job_type_part_time": "Part Time", "job_type_full_time": "Full Time", "basic_information": "Basic Information", "job_title": "Job Title", "job_description": "Job Description", "work_hours": "Work Hours", "work_days": "Work Days", "salary_min": "<PERSON><PERSON>", "salary_max": "<PERSON><PERSON>", "experience_level": "Experience Level", "positions": "Number of Positions", "tabs": {"basic_information": "Basic Information", "job_details": "Job Details", "skills": "Skills", "benefits": "Benefits", "required_documents": "Required Documents", "post_settings": "Post Settings"}, "featured_duration": "Featured Duration", "featured_duration_placeholder": "Select featured duration", "form": {"edit_post": "Edit Post", "create_new_post": "Create New Post"}, "status": {"draft": "Draft", "published": "Published"}, "documents": {"resume_cv": "Resume/CV"}, "actions": {"save_as_draft": "Save as Draft"}, "placeholders": {"job_title": "e.g. Senior Software Engineer", "company_name": "Company name", "job_location": "e.g. Ho <PERSON> Minh City", "job_description": "Detailed job description", "select_job_type": "Select job type", "select_work_type": "Select work type", "work_hours": "e.g. 9:00 AM - 6:00 PM", "work_days": "e.g. Monday - Friday", "salary": "e.g. $1000 - $1500 per month", "select_experience_level": "Select experience level", "positions": "Enter number of positions"}, "validation": {"please_enter_job_title": "Please enter job title", "please_enter_company_name": "Please enter company name", "please_enter_job_location": "Please enter job location", "please_enter_job_description": "Please enter job description", "at_least_one_day": "At least one work day must be selected", "set_time_ranges": "Please set time ranges for all active days"}, "benefits": {"title": "Benefits", "label": "Job Benefits", "placeholder": "Add a benefit (e.g. Health Insurance, Performance Bonus)", "add_benefit": "Add benefit", "remove_benefit": "Remove benefit", "no_benefits_added": "No benefits added yet. Start by adding your first benefit above."}, "skills": {"title": "Required Skills", "label": "Skills", "placeholder": "Add a skill (e.g. React, Project Management)", "add_skill": "Add skill", "remove_skill": "Remove skill", "no_skills_added": "No skills added yet. Start by adding your first skill above."}}, "roles": {"role_management": "Role Management", "add_role": "Add Role", "edit_role": "Edit Role", "delete_role": "Delete Role", "search_placeholder": "Search Role", "role_updated_successfully": "Role updated successfully", "role_update_failed": "Role update failed", "role_created_successfully": "Role created successfully", "role_creation_failed": "Role creation failed", "role_name": "Role Name", "enter_role_name": "Enter role name", "role_description_placeholder": "Describe what this role allows"}, "companies": {"company_management": "Company Management", "add_company": "Add Company", "edit_company": "Edit Company", "add_new_company": "Add New Company", "create_company": "Create Company", "update_company": "Update Company", "search_companies_placeholder": "Search companies...", "company_name": "Company Name", "company_description": "Company Description", "company_logo": "Company Logo", "company_type": "Company Type", "verification_status": "Verification Status", "contact_email": "Contact Email", "contact_phone": "Contact Phone", "website_url": "Website URL", "founded_year": "Founded Year", "teams_jobs": "Teams/Jobs", "team_members": "Team Members", "active_job_postings": "Active Job Postings", "individual_company": "Individual Company", "individual_company_help": "Individual companies represent freelancers or sole proprietors", "organization": "Organization", "individual": "Individual", "verified": "Verified", "not_verified": "Not Verified", "upload_logo": "Upload Logo", "company_created_successfully": "Company created successfully", "company_updated_successfully": "Company updated successfully", "company_deleted_successfully": "Company {name} has been deleted successfully", "company_verified_successfully": "Company {name} has been verified successfully", "failed_to_save_company": "Failed to save company", "failed_to_delete_company": "Failed to delete company", "failed_to_verify_company": "Failed to verify company", "verify_company_title": "Verify {name}?", "verify_company_message": "This will mark the company as verified. This action cannot be undone.", "enter_company_name": "Enter company name", "enter_company_address": "Enter company address", "enter_company_description": "Enter company description", "select_industry": "Select industry", "please_enter_company_name": "Please enter company name", "please_enter_company_address": "Please enter company address", "please_enter_company_description": "Please enter company description", "please_select_industry": "Please select an industry", "please_enter_valid_email": "Please enter a valid email", "please_enter_valid_url": "Please enter a valid URL", "industries": {"technology": "Technology", "healthcare": "Healthcare", "education": "Education", "finance": "Finance", "retail": "Retail", "manufacturing": "Manufacturing", "hospitality": "Hospitality", "other": "Other"}, "placeholders": {"company_name": "Enter company name", "contact_email": "<EMAIL>", "contact_phone": "+****************", "website_url": "https://www.company.com", "founded_year": "e.g. 2010", "company_address": "Enter company address", "company_description": "Enter company description"}}, "team_member": {"team_member_name": "Name", "team_member_management": "Team Member Management", "add_team_member": "Add Team Member", "edit_team_member": "Edit Team Member", "invite_member": "Invite Member", "update_member": "Update Member", "select_users": "Select Users", "search_and_select_users_placeholder": "Search and select users to invite as team members", "position": "Position", "branch": "Branch", "manager_role": "Manager Role", "manager_role_help": "Managers have additional permissions within the company", "default": "<PERSON><PERSON><PERSON>", "enter_email_placeholder": "Enter member's email address", "enter_position_placeholder": "Enter member's position/title", "select_branch_placeholder": "Select branch", "please_enter_email": "Please enter email address", "invitation_email_help": "An invitation will be sent to this email", "team_member_updated_successfully": "Team member updated successfully", "team_member_invited_successfully": "Team member invited successfully", "failed_to_save_team_member": "Failed to save team member", "failed_to_load_branches": "Failed to load branches"}, "table": {"filter_data": "Filter Data", "apply_filters": "Apply Filters", "clear_filters": "Clear", "enter_placeholder": "Enter {label}", "select_placeholder": "Select {label}", "confirm_delete": "Confirm Delete", "delete_confirmation_message": "Are you sure you want to delete this item?", "delete_confirmation_warning": "This action cannot be undone.", "deleted_successfully": "Deleted successfully", "delete_failed": "Failed to delete", "selected_items": "Selected {count} items"}, "address": {"province": "Province/City", "district": "District", "ward": "Ward", "detailed_address": "Detailed Address", "select_province": "Select province/city", "select_district": "Select district", "select_ward": "Select ward", "enter_detailed_address": "Enter house number, street name...", "search_province": "Search Province/City", "type_to_search_province": "Type to search province/city...", "full_address": "Full Address", "address_not_provided": "Address not provided", "loading_provinces": "Loading provinces/cities...", "loading_districts": "Loading districts...", "loading_wards": "Loading wards...", "error_loading_provinces": "Failed to load provinces/cities", "error_loading_districts": "Failed to load districts", "error_loading_wards": "Failed to load wards", "please_select_province_first": "Please select province/city first", "please_select_district_first": "Please select district first", "validation": {"province_required": "Please select province/city", "district_required": "Please select district", "ward_required": "Please select ward", "detail_address_required": "Please enter detailed address", "invalid_address": "Please enter a valid address"}, "copy": {"copy_address": "Copy address", "address_copied": "Address copied", "copy_failed": "Failed to copy address"}, "format": {"short_format": "Short format", "full_format": "Full format", "administrative_only": "Administrative units only"}}, "profile": {"title": "Profile: Detail Information", "edit": "Edit", "role": "Role", "role_admin": "Admin", "role_employer": "Employer", "role_employee": "Employee", "address": "Address", "gender": "Gender", "gender_male": "Male", "gender_female": "Female", "gender_other": "Other", "dob": "Date of Birth", "created_at": "Created At", "change_password": "Change password", "load_error": "Failed to load profile data", "save": "Save", "cancel": "Cancel", "old_password": "Old password", "new_password": "New password", "confirm_password": "Confirm new password", "password_required": "Password is required", "password_mismatch": "Passwords do not match", "password_format_error": "Password must be at least 8 characters long and contain at least one digit, one lowercase letter, one uppercase letter, and one special character", "edit_title": "Edit Profile", "save_success": "Profile updated successfully!", "update_success": "Profile updated successfully!", "update_error": "Failed to update profile!", "change_password_title": "Change Password", "change_success": "Password changed successfully!", "change_error": "Failed to change password!", "name": "Name", "phone_number": "Phone number", "na": "N/A"}, "wallet": {"wallet_management": "Wallet Management", "job_seeker_wallet_management": "Job Seeker Wallets", "employer_wallet_management": "Employer Wallets", "job_seeker_wallet_description": "Manage job seeker wallet points and transactions", "job_seeker_wallets": "Job Seeker Wallets", "employer_wallets": "Employer Wallets", "points": "Points", "points_unit": "pts", "search_placeholder": "Search users by name, email..."}, "job_seeker_post": {"validation": {"please_enter_job_title": "Please enter job title", "please_enter_company_name": "Please enter company name", "please_enter_job_location": "Please enter job location", "please_enter_job_description": "Please enter job description", "at_least_one_day": "At least one work day must be selected", "set_time_ranges": "Please set time ranges for all active days"}, "create_new_post": "Create New Post", "edit_post": "Edit Post", "work_type": "Work Type", "work_type_onsite": "On-site", "work_type_remote": "Remote", "work_type_hybrid": "Hybrid", "job_type_part_time": "Part Time", "job_type_full_time": "Full Time", "job_type": "Job Type", "featured": "Featured", "post_date_range": "Post Date Range", "search_posts_placeholder": "Search posts by title, company...", "title": "Job Seeker Post", "status": "Status", "status_pending": "Pending", "status_active": "Active", "status_draft": "Draft", "status_closed": "Closed", "status_expired": "Expired", "work_schedule": "Work Schedule", "salary_min": "<PERSON><PERSON>", "salary_max": "<PERSON><PERSON>", "experience_level": "Experience Level", "positions": "Number of Positions", "location": "Location", "basic_information": "Basic Information", "job_details": "Job Details", "skills": "Skills", "skills_placeholder": "Add a skill (e.g. React, Project Management)", "benefits": "Benefits", "required_documents": "Required Documents", "post_settings": "Post Settings", "languages_placeholder": "Add a language (e.g. English)", "add_language": "Add Language", "level": "Level", "education": "Education", "education_level": "Education Level", "education_detail": "Education Detail", "experiences": "Experiences", "add_experience": "Add Experience", "year_of_experience": "Years of Experience", "working_hour_per_day": "Working Hours/Day", "start_time": "Start Time", "end_time": "End Time", "jobType": "Job Type", "contractType": "Contract Type", "workType": "Work Type", "salary_period_hourly": "Hourly", "salary_period_daily": "Daily", "salary_period_weekly": "Weekly", "salary_period_monthly": "Monthly", "salary_period_yearly": "Yearly", "detail_address": "Detail Address", "isEnableEmailNotification": "Enable Email Notification", "isEnableInformation": "Show Information", "isAutoAcceptInterviewInvitation": "Auto Accept Interview Invitation", "isReady": "Ready", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "fluent": "Fluent", "native": "Native", "add_skill": "<PERSON><PERSON>", "opportunity_count": "Opportunities", "view_count": "Views", "status_paused": "Paused", "status_reopen": "Reopen", "status_rejected": "Rejected", "job_seeker_post_detail": "Job Seeker Post Detail", "new_opportunity_count": "New Opportunities", "featureJob": "Featured Job", "feature_duration": "Featured Duration", "interview_request_count": "Interview Requests", "delete_post_confirm": "Delete this post?", "delete_post_confirm_desc": "Are you sure you want to delete this post? This action cannot be undone.", "industry": "Industry", "skills_and_languages": "Skills & Languages", "languages": "Languages", "salary": "Salary", "salary_currency": "<PERSON><PERSON><PERSON><PERSON>", "salary_period": "Period", "other_information": "Other Information", "active_date": "Active Date", "statistics": "Statistics", "working_days": "Working Days", "working_shifts": "Working Shifts", "post_not_found": "Post not found", "failed_to_load_post_data": "Failed to load post data. Please try again.", "working_information": "Working Information", "features": "Features", "activate": "Activate", "activate_post_confirm": "Activate this post?", "activate_post_confirm_desc": "Are you sure you want to activate this post?", "close_post": "Close Post", "edit_job_seeker_post": "Edit Job <PERSON> Post"}}