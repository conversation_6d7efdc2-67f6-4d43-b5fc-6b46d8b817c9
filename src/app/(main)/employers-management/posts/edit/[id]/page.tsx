"use client";
import { useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import PostFormPage from "@/components/features/posts/PostFormPage";

export default function EditPostPageWrapper() {
  const authContext = useContext(AuthContext);
  const access = authContext?.access;
  if (!access?.[PermissionEnum.POST_UPDATE]) {
    return <div>Bạn không có quyền chỉnh sửa bài đăng.</div>;
  }
  return <PostFormPage isEdit={true} />;
}
