// app/(main)/employers-management/interviews/page.tsx
"use client";
import InterviewForm from "@/components/features/interviews/InterviewForm";
import InterviewStatsComponent from "@/components/features/interviews/InterviewStats";
import InterviewTable from "@/components/features/interviews/InterviewTable";
import { Interview } from "@/types/interview";
import { UserRole } from "@/constants/userRole";
import { Card, Modal } from "antd";
import React, { useState } from "react";

const EmployerInterviewsPage: React.FC = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(
    null
  );
  const [refreshKey, setRefreshKey] = useState(0);

  const handleCreateSuccess = () => {
    setCreateModalVisible(false);
    refreshData();
  };

  const handleEditSuccess = () => {
    setEditModalVisible(false);
    setSelectedInterview(null);
    refreshData();
  };

  const handleEditInterview = (interview: Interview) => {
    setSelectedInterview(interview);
    setEditModalVisible(true);
  };

  const refreshData = () => {
    setRefreshKey((prevKey) => prevKey + 1);
  };

  return (
    <>
      <Card className="!mb-4">
        {/* Statistics Section */}
        <InterviewStatsComponent
          key={`stats-${refreshKey}`}
          onRefresh={refreshData}
        />
      </Card>

      {/* Table Section */}
      <Card>
        <InterviewTable
          key={`table-${refreshKey}`}
          onEditInterview={handleEditInterview}
          userRole={UserRole.EMPLOYER}
        />
      </Card>

      {/* Create Interview Modal */}
      <Modal
        title="Schedule New Interview"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <InterviewForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setCreateModalVisible(false)}
        />
      </Modal>

      {/* Edit Interview Modal */}
      <Modal
        title="Edit Interview"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedInterview && (
          <InterviewForm
            interview={selectedInterview}
            onSuccess={handleEditSuccess}
            onCancel={() => setEditModalVisible(false)}
          />
        )}
      </Modal>
    </>
  );
};

export default EmployerInterviewsPage;
