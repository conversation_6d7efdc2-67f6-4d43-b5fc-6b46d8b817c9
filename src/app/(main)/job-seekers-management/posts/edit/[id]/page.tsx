"use client";
import { useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import JobSeekerPostFormPage from "@/components/features/posts/JobSeekerPostFormPage";

export default function EditPostPageWrapper() {
  const authContext = useContext(AuthContext);
  const access = authContext?.access;
  if (!access?.[PermissionEnum.POST_UPDATE]) {
    return <div><PERSON>ạn không có quyền chỉnh sửa bài đăng.</div>;
  }
  return <JobSeekerPostFormPage isEdit={true} />;
}
