"use client";
import JobSeekerPostService from "@/services/jobSeekerPostService";
import type { JobSeekerPost } from "@/types/post";
import {
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  BookOutlined,
  TrophyOutlined,
  <PERSON><PERSON><PERSON>cleOutlined,
  DollarOutlined,
  EnvironmentOutlined,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
} from "@ant-design/icons";
import {
  Alert,
  Button,
  Card,
  Popconfirm,
  Skeleton,
  Space,
  Typography,
  message,
  Tag,
  List,
} from "antd";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState, useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;

const SectionCard = ({
  title,
  icon,
  children,
}: {
  title: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
}) => (
  <Card
    className="mb-4"
    title={
      <span className="flex items-center gap-2">
        {icon}
        {title}
      </span>
    }
  >
    {children}
  </Card>
);

const InfoRow = ({
  label,
  value,
}: {
  label: string;
  value: React.ReactNode;
}) => (
  <div className="mb-2 flex gap-2">
    <Text strong>{label}:</Text> <span>{value}</span>
  </div>
);

const renderTags = (arr: string[] | undefined) =>
  arr && arr.length > 0 ? (
    <Space wrap>
      {arr.map((item) => (
        <Tag key={item}>{item}</Tag>
      ))}
    </Space>
  ) : (
    "-"
  );
const renderValue = (val: any) =>
  val !== undefined && val !== null && val !== "" ? val : "-";
const renderBool = (val: boolean) =>
  val ? (
    <CheckCircleTwoTone twoToneColor="#52c41a" />
  ) : (
    <CloseCircleTwoTone twoToneColor="#ff4d4f" />
  );
const renderLanguages = (
  arr: { language: string; level: string }[] | undefined
) =>
  arr && arr.length > 0 ? (
    <Space wrap>
      {arr.map((item, idx) => (
        <Tag key={idx}>
          {item.language} ({item.level})
        </Tag>
      ))}
    </Space>
  ) : (
    "-"
  );

const PostDetailPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const postId = params.id as string;
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const [post, setPost] = useState<JobSeekerPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const t = useTranslations("job_seeker_post");
  const tCommon = useTranslations("common");

  useEffect(() => {
    const fetchPostDetails = async () => {
      try {
        setLoading(true);
        const postData = await JobSeekerPostService.getDetail(postId);
        setPost(postData.data);
        setError(null);
      } catch (err) {
        console.error("Error fetching post details:", err);
        setError(
          t("failed_to_load_post_data") ||
            "Failed to load post details. Please try again."
        );
      } finally {
        setLoading(false);
      }
    };
    fetchPostDetails();
  }, [postId]);

  const detailsSection = post && (
    <div className="space-y-4">
      {/* Basic Info */}
      <SectionCard title={t("basic_information")} icon={<UserOutlined />}>
        <InfoRow label={t("title")} value={renderValue(post.title)} />
        <InfoRow
          label={t("industry") || "Industry"}
          value={renderValue(post.industry)}
        />
        <InfoRow
          label={tCommon("fields.status")}
          value={
            <Tag
              color={
                post.status === "active"
                  ? "green"
                  : post.status === "draft"
                  ? "gray"
                  : post.status === "pending"
                  ? "orange"
                  : post.status === "rejected"
                  ? "volcano"
                  : "default"
              }
            >
              {post.status}
            </Tag>
          }
        />
        <InfoRow
          label={tCommon("fields.description")}
          value={
            <div className="whitespace-pre-line">
              {renderValue(post.description)}
            </div>
          }
        />
      </SectionCard>

      {/* Skills & Languages */}
      <SectionCard
        title={t("skills_and_languages") || "Skills & Languages"}
        icon={<TrophyOutlined />}
      >
        <InfoRow label={t("skills")} value={renderTags(post.skills)} />
        <InfoRow
          label={t("languages") || "Languages"}
          value={renderLanguages(post.languages)}
        />
      </SectionCard>

      {/* Education */}
      <SectionCard
        title={t("education") || "Education"}
        icon={<BookOutlined />}
      >
        <InfoRow
          label={t("education_level") || "Education Level"}
          value={renderValue(post.educationLevel)}
        />
        <InfoRow
          label={t("education_detail") || "Education Detail"}
          value={renderValue(post.educationDetail)}
        />
      </SectionCard>

      {/* Experiences */}
      <SectionCard
        title={t("experiences") || "Experiences"}
        icon={<TrophyOutlined />}
      >
        {post.experiences && post.experiences.length > 0 ? (
          <List
            size="small"
            dataSource={post.experiences}
            renderItem={(exp) => (
              <List.Item>
                <span>
                  {exp.industry} - {t("year_of_experience") || "Years"}:{" "}
                  {exp.yearOfExperience}
                </span>
              </List.Item>
            )}
          />
        ) : (
          "-"
        )}
      </SectionCard>

      {/* Work Schedule */}
      <SectionCard title={t("work_schedule")} icon={<ClockCircleOutlined />}>
        <InfoRow
          label={t("working_days")}
          value={renderTags(post.workingDays)}
        />
        <InfoRow
          label={t("working_shifts")}
          value={renderTags(post.workingShifts)}
        />
        <InfoRow
          label={t("working_hour_per_day") || "Working Hour/Day"}
          value={renderValue(post.workingHourPerDay)}
        />
        <InfoRow
          label={t("start_time") || "Start Time"}
          value={renderValue(post.startTime)}
        />
        <InfoRow
          label={t("end_time") || "End Time"}
          value={renderValue(post.endTime)}
        />
        <InfoRow
          label={t("jobType") || "Job Type"}
          value={renderValue(post.jobType)}
        />
        <InfoRow
          label={t("contractType") || "Contract Type"}
          value={renderValue(post.contractType)}
        />
        <InfoRow
          label={t("workType") || "Work Type"}
          value={renderValue(post.workType)}
        />
      </SectionCard>

      {/* Salary */}
      <SectionCard title={t("salary") || "Salary"} icon={<DollarOutlined />}>
        <InfoRow
          label={t("salary_min") || "Min"}
          value={post.salary ? post.salary.min : "-"}
        />
        <InfoRow
          label={t("salary_max") || "Max"}
          value={
            post.salary && post.salary.max !== null ? post.salary.max : "-"
          }
        />
        <InfoRow
          label={t("salary_currency") || "Currency"}
          value={post.salary ? post.salary.currency : "-"}
        />
        <InfoRow
          label={t("salary_period") || "Period"}
          value={post.salary ? post.salary.period : "-"}
        />
      </SectionCard>

      {/* Location */}
      <SectionCard title={t("location")} icon={<EnvironmentOutlined />}>
        <InfoRow
          label={t("location") || "Location"}
          value={
            post.location
              ? `${post.location.provinceName || ""} ${
                  post.location.districtName || ""
                } ${post.location.wardName || ""}`.trim()
              : "-"
          }
        />
        <InfoRow
          label={t("detail_address") || "Detail Address"}
          value={post.location?.detailAddress || "-"}
        />
      </SectionCard>

      {/* Feature & Status */}
      <SectionCard
        title={t("other_information") || "Other Information"}
        icon={<CheckCircleTwoTone twoToneColor="#52c41a" />}
      >
        <InfoRow
          label={t("featureJob") || "Featured Job"}
          value={renderBool(post.isFeatureJob)}
        />
        <InfoRow
          label={t("feature_duration") || "Feature Duration"}
          value={renderValue(post.featureDuration)}
        />
        <InfoRow
          label={t("active_date") || "Active Date"}
          value={renderValue(post.activeDate)}
        />
        <InfoRow
          label={t("isEnableEmailNotification") || "Email Notification"}
          value={renderBool(post.isEnableEmailNotification)}
        />
        <InfoRow
          label={t("isEnableInformation") || "Show Info"}
          value={renderBool(post.isEnableInformation)}
        />
        <InfoRow
          label={
            t("isAutoAcceptInterviewInvitation") || "Auto Accept Interview"
          }
          value={renderBool(post.isAutoAcceptInterviewInvitation)}
        />
        <InfoRow
          label={t("isReady") || "Ready"}
          value={renderBool(post.isReady)}
        />
      </SectionCard>

      {/* Stats */}
      <SectionCard
        title={t("statistics") || "Statistics"}
        icon={<TrophyOutlined />}
      >
        <InfoRow
          label={t("opportunity_count") || "Opportunities"}
          value={renderValue(post.opportunityCount)}
        />
        <InfoRow
          label={t("new_opportunity_count") || "New Opportunities"}
          value={renderValue(post.newOpportunityCount)}
        />
        <InfoRow
          label={t("view_count") || "Views"}
          value={renderValue(post.viewCount)}
        />
        <InfoRow
          label={t("interview_request_count") || "Interview Requests"}
          value={renderValue(post.interviewRequestCount)}
        />
      </SectionCard>
    </div>
  );

  const handleEdit = () => {
    router.push(`/job-seekers-management/posts/edit/${postId}`);
  };

  const handleDelete = async () => {
    try {
      await JobSeekerPostService.delete(postId);
      message.success(tCommon("messages.success.deleted_successfully"));
      router.push("/job-seekers-management/posts");
    } catch (err) {
      console.error("Error deleting post:", err);
      message.error(tCommon("messages.error.failed_to_delete"));
    }
  };

  // Nếu cần status update, implement tương tự với JobSeekerPostService.update

  if (!access?.viewPost) {
    return <Alert message={tCommon("status.no_permission")} type="error" />;
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton active paragraph={{ rows: 1 }} />
        <Skeleton.Button
          active
          size="large"
          shape="square"
          style={{ width: 200, marginBottom: 16 }}
        />
        <Skeleton active paragraph={{ rows: 6 }} />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message={tCommon("status.error")}
        description={error}
        type="error"
      />
    );
  }

  if (!post) {
    return (
      <Alert message={t("post_not_found") || "Post not found"} type="warning" />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <Title level={3} className="mb-0">
          {t("job_seeker_post_detail") || "Job Seeker Post Detail"}
        </Title>
        <div className="flex items-center space-x-2 mt-2 md:mt-0">
          {access[PermissionEnum.POST_UPDATE] && (
            <Button icon={<EditOutlined />} onClick={handleEdit} size="large">
              {tCommon("actions.edit")}
            </Button>
          )}
          {access[PermissionEnum.POST_DELETE] && (
            <Popconfirm
              title={t("delete_post_confirm") || "Delete this post?"}
              description={
                t("delete_post_confirm_desc") ||
                "Are you sure you want to delete this post? This action cannot be undone."
              }
              onConfirm={handleDelete}
              okText={tCommon("actions.delete")}
              cancelText={tCommon("actions.cancel")}
              icon={<ExclamationCircleOutlined style={{ color: "red" }} />}
            >
              <Button danger icon={<DeleteOutlined />} size="large">
                {tCommon("actions.delete")}
              </Button>
            </Popconfirm>
          )}
        </div>
      </div>
      {detailsSection}
    </div>
  );
};

export default PostDetailPage;
