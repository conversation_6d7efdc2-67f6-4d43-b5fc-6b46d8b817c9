"use client";
import { utils } from "@/utils";
import { Form, Select, Typography } from "antd";
import { Rule } from "antd/lib/form";
import { SelectProps } from "antd/lib/select";
import { useTranslations } from "next-intl";
import React from "react";

const { Text } = Typography;

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

interface BaseSelectProps extends Omit<SelectProps, "options" | "name"> {
  label?: string;
  name?: string | (string | number)[];
  required?: boolean;
  error?: string;
  helpText?: string;
  options: SelectOption[];
  rules?: Rule[];
  className?: string;
}

const BaseSelect: React.FC<BaseSelectProps> = ({
  label,
  name,
  required = false,
  error,
  helpText,
  options,
  rules = [],
  className = "",
  placeholder = "Select an option",
  ...props
}) => {
  const tCommon = useTranslations("common");

  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `Please select ${label || "an option"}`,
    });
  }

  return (
    <Form.Item
      label={label}
      required={required}
      help={error || helpText}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
    >
      <Select
        placeholder={placeholder || tCommon("placeholders.please_select")}
        options={options}
        notFoundContent={tCommon("responses.no_data")}
        filterOption={utils.form.filterOption}
        {...props}
      />
      {helpText && !error && (
        <Text type="secondary" className="text-xs">
          {helpText}
        </Text>
      )}
    </Form.Item>
  );
};

export default BaseSelect;
