"use client";
import { Form, Upload, Typography } from "antd";
import { Rule } from "antd/lib/form";
import { UploadProps, UploadFile } from "antd/lib/upload";
import { UploadOutlined } from "@ant-design/icons";
import BaseButton from "../buttons/BaseButton";
import React from "react";
import { utils } from "@/utils";

const { Text } = Typography;

interface BaseUploadProps extends Omit<UploadProps, "fileList"> {
  label?: string;
  name?: string;
  required?: boolean;
  error?: string | null;
  helpText?: string;
  rules?: Rule[];
  className?: string;
  buttonText?: string;
  buttonIcon?: React.ReactNode;
  maxCount?: number;
  listType?: "text" | "picture" | "picture-card" | "picture-circle";
  action?: string;
  headers?: Record<string, string>;
  data?: Record<string, any> | ((file: UploadFile) => Record<string, any>);
  beforeUpload?: (file: File, fileList: File[]) => boolean | Promise<File>;
  onChange?: (info: any) => void;
  onRemove?: (file: UploadFile) => boolean | Promise<boolean>;
  showUploadList?: boolean | object;
  directory?: boolean;
  multiple?: boolean;
  disabled?: boolean;
}

const BaseUpload: React.FC<BaseUploadProps> = ({
  label,
  name,
  required = false,
  error,
  helpText,
  rules = [],
  className = "",
  buttonText = "Upload",
  buttonIcon = <UploadOutlined />,
  maxCount = 1,
  accept,
  listType = "text",
  headers,
  data,
  beforeUpload,
  onChange,
  onRemove,
  showUploadList = true,
  directory = false,
  multiple = false,
  disabled = false,
  ...props
}) => {
  // Add required rule if required prop is true
  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `Please upload ${label || "a file"}`,
      validator: (_, value) => {
        if (!value || (Array.isArray(value) && value.length === 0)) {
          return Promise.reject(
            new Error(`Please upload ${label || "a file"}`)
          );
        }
        return Promise.resolve();
      },
    });
  }

  const uploadUrl = process.env.NEXT_PUBLIC_API_URL || "";

  // Default headers with authorization token if available
  const defaultHeaders = {
    ...{
      authorization: "Bearer " + utils.auth.getAccessToken(),
    },
    ...headers,
  };

  return (
    <Form.Item
      label={label}
      required={required}
      help={error || helpText}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
      valuePropName="fileList"
      getValueFromEvent={(e) => {
        if (Array.isArray(e)) {
          return e;
        }
        return e?.fileList;
      }}
    >
      <Upload
        name="file"
        listType={listType}
        maxCount={maxCount}
        accept={accept}
        action={() => {
          return `${uploadUrl}/v1/files/upload`;
        }}
        headers={defaultHeaders}
        data={data}
        beforeUpload={beforeUpload}
        onChange={onChange}
        onRemove={onRemove}
        showUploadList={showUploadList}
        directory={directory}
        multiple={multiple}
        disabled={disabled}
        {...props}
      >
        {listType === "picture-card" ? (
          <div>
            {buttonIcon}
            <div style={{ marginTop: 8 }}>{buttonText}</div>
          </div>
        ) : (
          <BaseButton
            icon={buttonIcon}
            label={buttonText}
            disabled={disabled}
          />
        )}
      </Upload>
      {helpText && !error && (
        <Text type="secondary" className="text-xs mt-1 block">
          {helpText}
        </Text>
      )}
    </Form.Item>
  );
};

export default BaseUpload;
