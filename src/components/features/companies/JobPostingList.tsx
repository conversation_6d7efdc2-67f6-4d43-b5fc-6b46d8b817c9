import BaseTable from "@/components/ui/tables/BaseTable";
import type { FilterConfig } from "@/components/ui/tables/TableFilter";
import PostService from "@/services/postService";
import type { Post } from "@/types/post";
import type { TableColumnsType } from "antd";
import { Space, Tag } from "antd";
import { useRouter } from "next/navigation";

const JobPostingList = () => {
  const router = useRouter();

  // Define columns for the table
  const columns: TableColumnsType<Post> = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: "Title",
      dataIndex: "title",
      key: "title",
      ellipsis: true,
      width: 200,
      render: (text, record) => (
        <span>
          {text}
          {record.isFeatureJob && (
            <Tag color="gold" style={{ marginLeft: 8 }}>
              Featured
            </Tag>
          )}
          {record.urgentHiring && (
            <Tag color="volcano" style={{ marginLeft: 8 }}>
              Urgent
            </Tag>
          )}
        </span>
      ),
    },
    {
      title: "Company",
      dataIndex: "company",
      key: "company",
      width: 150,
    },
    {
      title: "Location",
      dataIndex: "location",
      key: "location",
      width: 150,
    },
    {
      title: "Type",
      dataIndex: "jobType",
      key: "jobType",
      width: 120,
    },
    {
      title: "Applications",
      dataIndex: "applicationCount",
      key: "applicationCount",
      width: 120,
      render: (text, record) => (
        <Space>
          {text}
          {record.applicationCount > 0 && (
            <Tag color="green">+{record.applicationCount} new</Tag>
          )}
        </Space>
      ),
    },
    {
      title: "Post Date",
      dataIndex: "postDate",
      key: "postDate",
      width: 120,
    },
    {
      title: "Expire Date",
      dataIndex: "expireDate",
      key: "expireDate",
      width: 120,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 120,
    },
  ];

  const filters: FilterConfig[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { value: "Đang hiển thị", label: "Đang hiển thị" },
        { value: "Bản nháp", label: "Bản nháp" },
        { value: "Đã đóng", label: "Đã đóng" },
        { value: "Hết hạn", label: "Hết hạn" },
      ],
    },
    {
      key: "workType",
      label: "Work Type",
      type: "select",
      options: [
        { value: "Tại chỗ", label: "Tại chỗ" },
        { value: "Từ xa", label: "Từ xa" },
        { value: "Kết hợp", label: "Kết hợp" },
      ],
    },
    {
      key: "jobType",
      label: "Job Type",
      type: "select",
      options: [
        { value: "Bán thời gian", label: "Bán thời gian" },
        { value: "Toàn thời gian", label: "Toàn thời gian" },
      ],
    },
    {
      key: "isFeatured",
      label: "Featured",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
    },
    {
      key: "dateRange",
      label: "Post Date Range",
      type: "dateRange",
    },
  ];

  // Handle create new post
  const handleCreate = () => {
    router.push("/features/posts/new");
  };

  // Handle edit post
  const handleEdit = (record: Post) => {
    router.push(`/features/posts/edit/${record.id}`);
  };

  // Handle delete post
  const handleDelete = async (record: Post) => {
    try {
      console.log("🚀 ~ handleDelete ~ record:", record);
    } catch (error) {
      console.error("Error deleting post:", error);
    }
  };

  // Handle row click to view post details
  const handleRowClick = (record: Post) => {
    router.push(`/features/posts/${record.id}`);
  };

  return (
    <div>
      <BaseTable<Post>
        api={PostService.getList}
        columns={columns}
        rowKey="id"
        title=""
        createBtnText="Create New Post"
        showSearch={true}
        searchPlaceholder="Search posts by title, company..."
        onCreate={handleCreate}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onRowClick={handleRowClick}
        showActions={true}
        filters={filters}
      />
    </div>
  );
};

export default JobPostingList;
