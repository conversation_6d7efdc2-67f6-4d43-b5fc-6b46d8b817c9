"use client";
import BaseTable from "@/components/ui/tables/BaseTable";
import type { FilterConfig } from "@/components/ui/tables/TableFilter";
import PostService from "@/services/postService";
import type { Post } from "@/types/post";
import type { TableColumnsType } from "antd";
import { Flex, Space, Tag, notification } from "antd";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import React, { useContext } from "react";
import dayjs from "dayjs";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { useCallback, useEffect, useState } from "react";

const EmployerTable: React.FC = () => {
  const router = useRouter();
  const t = useTranslations("posts");
  const tCommon = useTranslations("common");
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  // Define columns for the table
  const columns: TableColumnsType<Post> = [
    {
      title: tCommon("fields.id"),
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: t("title"),
      dataIndex: "title",
      key: "title",
      ellipsis: true,
      width: 200,
      render: (text, record) => (
        <span>
          {text}
          {record.isFeatureJob && (
            <Tag color="gold" style={{ marginLeft: 8 }}>
              {t("featured")}
            </Tag>
          )}
          {record.urgentHiring && (
            <Tag color="volcano" style={{ marginLeft: 8 }}>
              {t("urgent")}
            </Tag>
          )}
        </span>
      ),
    },
    {
      title: t("company"),
      dataIndex: "companyName",
      key: "companyName",
      width: 150,
      render: (text) => text || "-",
    },
    {
      title: t("location"),
      dataIndex: "location",
      key: "location",
      width: 150,
      render: (text) => text || "-",
    },
    {
      title: t("job_type"),
      dataIndex: "jobType",
      key: "jobType",
      width: 120,
      render: (text) => {
        const jobTypeMap: Record<string, string> = {
          part_time: t("job_type_part_time"),
          full_time: t("job_type_full_time"),
        };
        return jobTypeMap[text] || text || "-";
      },
    },
    {
      title: t("applications"),
      dataIndex: "applicationCount",
      key: "applicationCount",
      width: 120,
      render: (text, record) => (
        <Space>
          {text || 0}
          {record.newApplications > 0 && (
            <Tag color="green">
              +{record.newApplications} {t("new")}
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: t("post_date"),
      dataIndex: "postDate",
      key: "postDate",
      width: 120,
      render: (text) => (text ? dayjs(text).format("DD/MM/YYYY") : "-"),
    },
    {
      title: t("expire_date"),
      dataIndex: "expireDate",
      key: "expireDate",
      width: 120,
      render: (text) => (text ? dayjs(text).format("DD/MM/YYYY") : "-"),
    },
    {
      title: tCommon("fields.status"),
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (text) => {
        const statusMap: Record<string, { label: string; color: string }> = {
          pending: { label: t("status_pending"), color: "orange" },
          published: { label: t("status_active"), color: "green" },
          draft: { label: t("status_draft"), color: "gray" },
          closed: { label: t("status_closed"), color: "red" },
          expired: { label: t("status_expired"), color: "red" },
        };
        const statusInfo = statusMap[text] || {
          label: text || "-",
          color: "default",
        };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
  ];

  // Define filter configurations
  const filters: FilterConfig[] = [
    {
      key: "status",
      label: tCommon("fields.status"),
      type: "select",
      options: [
        { value: "pending", label: t("status_pending") },
        { value: "active", label: t("status_active") },
        { value: "draft", label: t("status_draft") },
        { value: "closed", label: t("status_closed") },
        { value: "expried", label: t("status_expired") },
      ],
    },
    {
      key: "workType",
      label: t("work_type"),
      type: "select",
      options: [
        { value: "onsite", label: t("work_type_onsite") },
        { value: "remote", label: t("work_type_remote") },
        { value: "hybrid", label: t("work_type_hybrid") },
      ],
    },
    {
      key: "jobType",
      label: t("job_type"),
      type: "select",
      options: [
        { value: "part_time", label: t("job_type_part_time") },
        { value: "full_time", label: t("job_type_full_time") },
      ],
    },
    {
      key: "isFeatured",
      label: t("featured"),
      type: "select",
      options: [
        { value: "true", label: tCommon("responses.yes") },
        { value: "false", label: tCommon("responses.no") },
      ],
    },
    {
      key: "dateRange",
      label: t("post_date_range"),
      type: "dateRange",
    },
  ];

  // Handle create new post
  const handleCreate = () => {
    router.push("/employers-management/posts/new");
  };

  // Handle edit post
  const handleEdit = (record: Post) => {
    router.push(`/employers-management/posts/edit/${record.id}`);
  };

  const handleDelete = async (record: Post) => {
    try {
      await PostService.delete(record.id);
      notification.success({
        message: t("messages.delete_success"),
        description: t("messages.post_deleted_successfully"),
      });
    } catch (error) {
      notification.error({
        message: t("messages.delete_error"),
        description: t("messages.failed_to_delete_post"),
      });
      console.error("Error deleting post:", error);
    }
  };

  // Handle row click to view post details
  const handleRowClick = (record: Post) => {
    router.push(`/employers-management/posts/${record.id}`);
  };

  return (
    <Flex vertical gap="middle">
      <BaseTable<Post>
        api={PostService.getList}
        columns={columns}
        rowKey="id"
        createBtnText={t("create_new_post")}
        showSearch={true}
        searchPlaceholder={t("search_posts_placeholder")}
        onCreate={
          access?.[PermissionEnum.POST_CREATE] ? handleCreate : undefined
        }
        onEdit={access?.[PermissionEnum.POST_UPDATE] ? handleEdit : undefined}
        onDelete={
          access?.[PermissionEnum.POST_DELETE] ? handleDelete : undefined
        }
        onRowClick={handleRowClick}
        showActions={true}
        filters={filters}
      />
    </Flex>
  );
};

export default EmployerTable;
