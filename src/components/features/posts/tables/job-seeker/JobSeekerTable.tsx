"use client";
import BaseTable from "@/components/ui/tables/BaseTable";
import type { FilterConfig } from "@/components/ui/tables/TableFilter";
import PostService from "@/services/postService";
import type { Post } from "@/types/post";
import type { TableColumnsType } from "antd";
import { Flex, Space, Tag, Modal, message } from "antd";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import React, { useContext } from "react";
import dayjs from "dayjs";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import JobSeekerPostService from "@/services/jobSeekerPostService";
import type {
  JobSeekerPost,
  JobSeekerPostWorkingDay,
  JobSeekerPostWorkingShift,
} from "@/types/post";
import { JobSeekerPostStatus } from "@/types/post";

const JobSeekerPostTable: React.FC = () => {
  const router = useRouter();
  const t = useTranslations("job_seeker_post");
  const tCommon = useTranslations("common");
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  // Define columns for the table
  const columns: TableColumnsType<JobSeekerPost> = [
    {
      title: tCommon("fields.id"),
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: t("title"),
      dataIndex: "title",
      key: "title",
      ellipsis: true,
      width: 200,
      render: (text, record) => (
        <span>
          {text}
          {record.isFeatureJob && (
            <Tag color="gold" style={{ marginLeft: 8 }}>
              {t("featured")}
            </Tag>
          )}
        </span>
      ),
    },
    {
      title: t("location"),
      dataIndex: "location",
      key: "location",
      width: 200,
      render: (location) =>
        location
          ? `${location.provinceName || ""} ${location.districtName || ""} ${
              location.wardName || ""
            }`.trim()
          : "-",
    },
    {
      title: t("skills"),
      dataIndex: "skills",
      key: "skills",
      width: 180,
      render: (skills: string[]) =>
        skills && skills.length > 0 ? (
          <Space wrap>
            {skills.map((skill) => (
              <Tag key={skill}>{skill}</Tag>
            ))}
          </Space>
        ) : (
          "-"
        ),
    },
    {
      title: t("working_days"),
      dataIndex: "workingDays",
      key: "workingDays",
      width: 160,
      render: (days: JobSeekerPostWorkingDay[]) =>
        days && days.length > 0 ? days.join(", ") : "-",
    },
    {
      title: t("working_shifts"),
      dataIndex: "workingShifts",
      key: "workingShifts",
      width: 140,
      render: (shifts: JobSeekerPostWorkingShift[]) =>
        shifts && shifts.length > 0 ? shifts.join(", ") : "-",
    },
    {
      title: t("opportunity_count"),
      dataIndex: "opportunityCount",
      key: "opportunityCount",
      width: 120,
      render: (text, record) => (
        <Space>
          {text || 0}
          {record.newOpportunityCount > 0 && (
            <Tag color="green">
              +{record.newOpportunityCount} {t("new")}
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: t("view_count"),
      dataIndex: "viewCount",
      key: "viewCount",
      width: 100,
    },
    {
      title: tCommon("fields.status"),
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status: JobSeekerPostStatus) => {
        const statusMap: Record<
          JobSeekerPostStatus,
          { label: string; color: string }
        > = {
          [JobSeekerPostStatus.PENDING]: {
            label: t("status_pending"),
            color: "orange",
          },
          [JobSeekerPostStatus.ACTIVE]: {
            label: t("status_active"),
            color: "green",
          },
          [JobSeekerPostStatus.DRAFT]: {
            label: t("status_draft"),
            color: "gray",
          },
          [JobSeekerPostStatus.CLOSED]: {
            label: t("status_closed"),
            color: "red",
          },
          [JobSeekerPostStatus.EXPIRED]: {
            label: t("status_expired"),
            color: "red",
          },
          [JobSeekerPostStatus.PAUSED]: {
            label: t("status_paused"),
            color: "default",
          },
          [JobSeekerPostStatus.REOPEN]: {
            label: t("status_reopen"),
            color: "blue",
          },
          [JobSeekerPostStatus.REJECTED]: {
            label: t("status_rejected"),
            color: "volcano",
          },
        };
        const statusInfo = statusMap[status] || {
          label: status,
          color: "default",
        };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
  ];

  // Define filter configurations
  const filters: FilterConfig[] = [
    {
      key: "status",
      label: tCommon("fields.status"),
      type: "select",
      options: [
        { value: "pending", label: t("status_pending") },
        { value: "active", label: t("status_active") },
        { value: "draft", label: t("status_draft") },
        { value: "closed", label: t("status_closed") },
        { value: "expried", label: t("status_expired") },
      ],
    },
    {
      key: "workType",
      label: t("work_type"),
      type: "select",
      options: [
        { value: "onsite", label: t("work_type_onsite") },
        { value: "remote", label: t("work_type_remote") },
        { value: "hybrid", label: t("work_type_hybrid") },
      ],
    },
    {
      key: "jobType",
      label: t("job_type"),
      type: "select",
      options: [
        { value: "part_time", label: t("job_type_part_time") },
        { value: "full_time", label: t("job_type_full_time") },
      ],
    },
    {
      key: "isFeatured",
      label: t("featured"),
      type: "select",
      options: [
        { value: "true", label: tCommon("responses.yes") },
        { value: "false", label: tCommon("responses.no") },
      ],
    },
    {
      key: "dateRange",
      label: t("post_date_range"),
      type: "dateRange",
    },
  ];

  // Handle create new post
  const handleCreate = () => {
    router.push("/job-seekers-management/posts/new");
  };

  // Handle edit post
  const handleEdit = (record: JobSeekerPost) => {
    router.push(`/job-seekers-management/posts/edit/${record.id}`);
  };

  // Handle delete post
  const handleDelete = async (record: JobSeekerPost) => {
    Modal.confirm({
      title: tCommon("messages.confirm_delete"),
      content: tCommon("messages.delete_confirmation_message"),
      okText: tCommon("actions.delete"),
      okType: "danger",
      cancelText: tCommon("actions.cancel"),
      onOk: async () => {
        try {
          await JobSeekerPostService.delete(record.id.toString());
          message.success(tCommon("messages.success.deleted_successfully"));
        } catch (error) {
          message.error(tCommon("messages.error.failed_to_delete"));
        }
      },
    });
  };

  // Handle row click to view post details
  const handleRowClick = (record: JobSeekerPost) => {
    router.push(`/job-seekers-management/posts/${record.id}`);
  };

  return (
    <Flex vertical gap="middle">
      <BaseTable<JobSeekerPost>
        api={JobSeekerPostService.getList}
        columns={columns}
        rowKey="id"
        createBtnText={t("create_new_post")}
        showSearch={true}
        searchPlaceholder={t("search_posts_placeholder")}
        onCreate={
          access?.[PermissionEnum.POST_CREATE] ? handleCreate : undefined
        }
        onEdit={access?.[PermissionEnum.POST_UPDATE] ? handleEdit : undefined}
        onDelete={
          access?.[PermissionEnum.POST_DELETE] ? handleDelete : undefined
        }
        onRowClick={handleRowClick}
        showActions={true}
        filters={filters}
      />
    </Flex>
  );
};

export default JobSeekerPostTable;
