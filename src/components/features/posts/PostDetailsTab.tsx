"use client";
import type { Post } from "@/types/post";
import { Card, Descriptions, Space, Tag, Typography } from "antd";
import React from "react";
import dayjs from "dayjs";

const { Text, Paragraph } = Typography;

interface PostDetailsTabProps {
  post: Post;
}

const PostDetailsTab: React.FC<PostDetailsTabProps> = ({ post }) => {
  return (
    <Card>
      <Descriptions layout="vertical" bordered>
        <Descriptions.Item label="Company" span={2}>
          {post.companyName || ""}
        </Descriptions.Item>
        <Descriptions.Item label="Location">{post.location}</Descriptions.Item>
        <Descriptions.Item label="Post Date">
          {post.postDate ? dayjs(post.postDate).format("DD/MM/YYYY") : ""}
        </Descriptions.Item>
        <Descriptions.Item label="Expire Date">
          {post.expireDate ? dayjs(post.expireDate).format("DD/MM/YYYY") : ""}
        </Descriptions.Item>
        <Descriptions.Item label="Positions">
          {post.positions}
        </Descriptions.Item>
        <Descriptions.Item label="Job Type">{post.jobType}</Descriptions.Item>
        <Descriptions.Item label="Salary">
          {post.salary && typeof post.salary === "object"
            ? `${post.salary.min} - ${post.salary.max} ${post.salary.currency} / ${post.salary.period}`
            : post.salary}
        </Descriptions.Item>
        {post?.workingInformation &&
          Array.isArray(post?.workingInformation) &&
          post?.workingInformation?.length > 0 && (
            <Descriptions.Item label="Working Information" span={3}>
              <Space direction="vertical">
                {post?.workingInformation?.map((info, idx) => (
                  <Text key={idx}>
                    {info.workDays}:{" "}
                    {typeof info.workHours === "string"
                      ? info.workHours
                      : JSON.stringify(info.workHours)}
                  </Text>
                ))}
              </Space>
            </Descriptions.Item>
          )}
        <Descriptions.Item label="Experience">
          {post.experienceLevel}
        </Descriptions.Item>
        <Descriptions.Item label="Features" span={3}>
          <Space wrap>
            {post.isFeatureJob && <Tag color="gold">Featured</Tag>}
            {post.urgentHiring && <Tag color="volcano">Urgent Hiring</Tag>}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="Skills" span={3}>
          <Space wrap>
            {post?.skills?.map((skill, index) => (
              <Tag key={index} color="blue">
                {skill}
              </Tag>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="Benefits" span={3}>
          <Space direction="vertical">
            {post?.benefits?.map((benefit, index) => (
              <Text key={index}>• {benefit}</Text>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="Required Documents" span={3}>
          <Space direction="vertical">
            {post?.requiredDocuments?.map((doc, index) => (
              <Text key={index}>
                • {doc.name}{" "}
                {doc.required ? (
                  <Tag color="red">Required</Tag>
                ) : (
                  <Tag color="blue">Optional</Tag>
                )}
              </Text>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="Description" span={3}>
          <Paragraph ellipsis={{ rows: 4, expandable: true, symbol: "more" }}>
            {post.description}
          </Paragraph>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default PostDetailsTab;
