"use client";
import { Card, DatePicker, Flex, Form, Select, Switch } from "antd";
import dayjs from "dayjs";
import React from "react";
import { useTranslations } from "next-intl";

const PostSettingsTab: React.FC = () => {
  const isFeatured = Form.useWatch("isFeatured");
  const t = useTranslations("posts");

  const featuredDurationOptions = [
    { value: 1, label: "1 day" },
    { value: 3, label: "3 days" },
    { value: 5, label: "5 days" },
    { value: 7, label: "7 days" },
  ];

  return (
    <Card title="Post Settings" className="mb-6">
      <Flex gap="middle">
        <Form.Item
          name="postDate"
          label="Post Date"
          rules={[{ required: true, message: "Please select post date" }]}
          className="w-1/2"
          getValueProps={(value) => {
            return {
              value: value ? dayjs(value) : undefined,
            };
          }}
        >
          <DatePicker style={{ width: "100%" }} />
        </Form.Item>

        <Form.Item
          name="expireDate"
          label="Expiry Date"
          rules={[{ required: true, message: "Please select expiry date" }]}
          className="w-1/2"
          getValueProps={(value) => {
            return {
              value: value ? dayjs(value) : undefined,
            };
          }}
        >
          <DatePicker style={{ width: "100%" }} />
        </Form.Item>
      </Flex>

      <Flex gap="middle">
        <Form.Item
          name="isFeatured"
          label="Featured Post"
          valuePropName="checked"
          className="w-1/2"
        >
          <Switch checkedChildren="Yes" unCheckedChildren="No" />
        </Form.Item>

        <Form.Item
          name="urgentHiring"
          label="Urgent Hiring"
          valuePropName="checked"
          className="w-1/2"
        >
          <Switch checkedChildren="Yes" unCheckedChildren="No" />
        </Form.Item>
      </Flex>

      {isFeatured && (
        <Flex gap="middle">
          <Form.Item
            name="featuredDuration"
            label={t("featured_duration")}
            rules={[
              { required: true, message: "Please select featured duration" },
            ]}
            className="w-1/2"
          >
            <Select
              placeholder={t("featured_duration_placeholder")}
              options={featuredDurationOptions}
              style={{ width: "100%" }}
            />
          </Form.Item>

          <div className="w-1/2" />
        </Flex>
      )}
    </Card>
  );
};

export default PostSettingsTab;
