"use client";
import type { Post, PostStatus } from "@/types/post";
import PostService from "@/services/postService";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  PauseCircleOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Flex,
  message,
  Space,
  Tag,
  Typography,
  Popconfirm,
} from "antd";
import React from "react";

const { Text } = Typography;

interface PostStatusCardProps {
  post: Post;
  onStatusChange: (newStatus: PostStatus) => Promise<void>;
}

const PostStatusCard: React.FC<PostStatusCardProps> = ({
  post,
  onStatusChange,
}) => {
  const handleActivatePost = async () => {
    await PostService.activate(post.id.toString());
    message.success("Post activated successfully");
    window.location.reload();
  };

  return (
    <Card>
      <Flex justify="space-between" align="center">
        <Flex align="center" gap="small">
          <Text strong>Title:</Text>
          {post.title}
        </Flex>
        <Flex align="center" gap="small">
          <Text strong>Status:</Text>
          {/* <Tag color={PostService.getStatusColor(post.status)}> */}
          {post.status}
          {/* </Tag> */}
        </Flex>
        <Space>
          {/* {post.status !== "Đang hiển thị" && (
            <Button
              icon={<CheckCircleOutlined />}
              type="primary"
              onClick={() => onStatusChange("Đang hiển thị")}
            >
              Activate
            </Button>
          )} */}
          {post.status === "pending" && (
            <Popconfirm
              title="Activate this post?"
              description="Are you sure you want to activate this post?"
              onConfirm={handleActivatePost}
              okText="Yes"
              cancelText="No"
            >
              <Button icon={<CheckCircleOutlined />} type="primary">
                Activate
              </Button>
            </Popconfirm>
          )}
          {post.status !== "Bản nháp" && (
            <Button
              icon={<PauseCircleOutlined />}
              onClick={() => onStatusChange("Bản nháp")}
            >
              Save as Draft
            </Button>
          )}
          {post.status !== "Đã đóng" && (
            <Button
              icon={<CloseCircleOutlined />}
              danger
              onClick={() => onStatusChange("Đã đóng")}
            >
              Close Post
            </Button>
          )}
        </Space>
      </Flex>
    </Card>
  );
};

export default PostStatusCard;
