import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  Form,
  Input,
  Button,
  Select,
  Switch,
  message,
  Spin,
  Space,
  Card,
  Typography,
  InputNumber,
  DatePicker,
  Row,
  Col,
} from "antd";
import JobSeekerPostService from "@/services/jobSeekerPostService";
import type { JobSeekerPost } from "@/types/post";
import { useTranslations } from "next-intl";
import {
  SaveOutlined,
  ArrowLeftOutlined,
  UserOutlined,
  BookOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  EnvironmentOutlined,
  PlusOutlined,
  DeleteOutlined,
  GlobalOutlined,
  BankOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

const { TextArea } = Input;
const { Title } = Typography;

interface Props {
  isEdit?: boolean;
}

const statusOptions = [
  { value: "active", label: "Active" },
  { value: "draft", label: "Draft" },
  { value: "pending", label: "Pending" },
  { value: "rejected", label: "Rejected" },
  { value: "expired", label: "Expired" },
  { value: "paused", label: "Paused" },
  { value: "closed", label: "Closed" },
  { value: "reopen", label: "Reopen" },
];
const workingDayOptions = [
  { value: "monday", label: "Monday" },
  { value: "tuesday", label: "Tuesday" },
  { value: "wednesday", label: "Wednesday" },
  { value: "thursday", label: "Thursday" },
  { value: "friday", label: "Friday" },
  { value: "saturday", label: "Saturday" },
  { value: "sunday", label: "Sunday" },
];
const workingShiftOptions = [
  { value: "morning", label: "Morning" },
  { value: "afternoon", label: "Afternoon" },
  { value: "evening", label: "Evening" },
];
const featureDurationOptions = [
  { value: 1, label: "1" },
  { value: 3, label: "3" },
  { value: 5, label: "5" },
  { value: 7, label: "7" },
];

const languageLevelOptions = [
  { value: "beginner", label: "Beginner" },
  { value: "intermediate", label: "Intermediate" },
  { value: "advanced", label: "Advanced" },
  { value: "fluent", label: "Fluent" },
  { value: "native", label: "Native" },
];

const JobSeekerPostFormPage: React.FC<Props> = ({ isEdit }) => {
  const [form] = Form.useForm();
  const params = useParams();
  const router = useRouter();
  const t = useTranslations("job_seeker_post");
  const tCommon = useTranslations("common");
  const [loading, setLoading] = useState(false);
  const [initialValues, setInitialValues] = useState<Partial<JobSeekerPost>>(
    {}
  );

  const postId = params.id as string;

  // moved currencyOptions, periodOptions here to use t
  const currencyOptions = [
    { value: "VND", label: "VND" },
    { value: "USD", label: "USD" },
    { value: "EUR", label: "EUR" },
    { value: "GBP", label: "GBP" },
    { value: "CNY", label: "CNY" },
  ];
  const periodOptions = [
    { value: "hourly", label: t("salary_period_hourly") || "Hourly" },
    { value: "daily", label: t("salary_period_daily") || "Daily" },
    { value: "weekly", label: t("salary_period_weekly") || "Weekly" },
    { value: "monthly", label: t("salary_period_monthly") || "Monthly" },
    { value: "yearly", label: t("salary_period_yearly") || "Yearly" },
  ];

  useEffect(() => {
    if (isEdit && postId) {
      setLoading(true);
      JobSeekerPostService.getDetail(postId)
        .then((res) => {
          // convert date string to dayjs for DatePicker
          const data = {
            ...res.data,
            startTime: res.data.startTime
              ? dayjs(res.data.startTime)
              : undefined,
            endTime: res.data.endTime ? dayjs(res.data.endTime) : undefined,
          };
          setInitialValues(data);
          form.setFieldsValue(data);
        })
        .catch(() => message.error("Failed to load post data"))
        .finally(() => setLoading(false));
    }
  }, [isEdit, postId, form]);

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // convert dayjs to string
      const submitData = {
        ...values,
        startTime: values.startTime ? values.startTime.format() : null,
        endTime: values.endTime ? values.endTime.format() : null,
      };
      if (isEdit && postId) {
        await JobSeekerPostService.update(postId, submitData);
        message.success(tCommon("messages.success.updated_successfully"));
        router.push(`/job-seekers-management/posts/${postId}`);
      } else {
        await JobSeekerPostService.create(submitData);
        message.success(tCommon("messages.success.created_successfully"));
        router.push(`/job-seekers-management/posts`);
      }
    } catch (err) {
      message.error(tCommon("messages.error.failed_to_save_post"));
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center min-h-[300px]">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => router.back()}
                size="large"
              />
              <Title level={3} style={{ margin: 0 }}>
                {isEdit
                  ? t("edit_job_seeker_post") || "Edit Job Seeker Post"
                  : t("create_new_post")}
              </Title>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={() => form.submit()}
              size="large"
              loading={loading}
            >
              {isEdit ? tCommon("actions.update") : tCommon("actions.create")}
            </Button>
          </Col>
        </Row>
      </Card>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={onFinish}
        autoComplete="off"
        requiredMark={false}
      >
        <Row gutter={[24, 0]}>
          {/* Left Column */}
          <Col xs={24} lg={12}>
            {/* Basic Info */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <UserOutlined /> {t("basic_information")}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item
                name="title"
                label={t("title")}
                rules={[
                  {
                    required: true,
                    message: t("validation.please_enter_job_title"),
                  },
                ]}
              >
                {" "}
                <Input size="large" />{" "}
              </Form.Item>
              <Form.Item name="industry" label={t("industry")}>
                {" "}
                <Input size="large" />{" "}
              </Form.Item>
              <Form.Item name="status" label={t("status")}>
                {" "}
                <Select options={statusOptions} size="large" />{" "}
              </Form.Item>
              <Form.Item
                name="description"
                label={tCommon("fields.description")}
              >
                {" "}
                <TextArea rows={4} showCount maxLength={500} />{" "}
              </Form.Item>
            </Card>

            {/* Skills */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <TrophyOutlined /> {t("skills")}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="skills">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={20}>
                            <Form.Item
                              {...restField}
                              name={name}
                              label="Skill"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter skill",
                                },
                              ]}
                            >
                              <Input
                                placeholder={
                                  t("skills_placeholder") ||
                                  "e.g., JavaScript, React, Node.js"
                                }
                                size="large"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={4}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      {t("add_skill") || "Add Skill"}
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>
            {/* Languages */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <GlobalOutlined /> {t("languages")}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="languages">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "language"]}
                              label="Language"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter language",
                                },
                              ]}
                            >
                              <Input
                                placeholder="e.g., English, Vietnamese"
                                size="large"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, "level"]}
                              label="Level"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select level",
                                },
                              ]}
                            >
                              <Select
                                options={languageLevelOptions}
                                placeholder="Select level"
                                size="large"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      {t("add_language") || "Add Language"}
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>
          </Col>

          {/* Right Column */}
          <Col xs={24} lg={12}>
            {/* Education */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <BookOutlined /> {t("education") || "Education"}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item name="educationLevel" label={t("education_level")}>
                <Input size="large" />
              </Form.Item>
              <Form.Item name="educationDetail" label={t("education_detail")}>
                <Input size="large" />
              </Form.Item>
            </Card>

            {/* Experiences */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <BankOutlined /> {t("experiences") || "Experiences"}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="experiences">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "industry"]}
                              label="Industry"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter industry",
                                },
                              ]}
                            >
                              <Input
                                placeholder="e.g., Software Development"
                                size="large"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, "yearOfExperience"]}
                              label="Years of Experience"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter years of experience",
                                },
                              ]}
                            >
                              <InputNumber
                                min={0}
                                max={50}
                                placeholder="Years"
                                style={{ width: "100%" }}
                                size="large"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      {t("add_experience") || "Add Experience"}
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>

            {/* Work Schedule */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <ClockCircleOutlined />{" "}
                  {t("work_schedule") || "Work Schedule"}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item name="workingDays" label={t("working_days")}>
                <Select
                  mode="multiple"
                  options={workingDayOptions}
                  size="large"
                />
              </Form.Item>
              <Form.Item name="workingShifts" label={t("working_shifts")}>
                <Select
                  mode="multiple"
                  options={workingShiftOptions}
                  size="large"
                />
              </Form.Item>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="workingHourPerDay"
                    label={t("working_hour_per_day")}
                  >
                    <InputNumber
                      min={1}
                      max={24}
                      style={{ width: "100%" }}
                      size="large"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="jobType" label={t("jobType")}>
                    <Input size="large" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="startTime" label={t("start_time")}>
                    <DatePicker
                      showTime
                      style={{ width: "100%" }}
                      size="large"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="endTime" label={t("end_time")}>
                    <DatePicker
                      showTime
                      style={{ width: "100%" }}
                      size="large"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="contractType" label={t("contractType")}>
                    <Input size="large" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="workType" label={t("workType")}>
                    <Input size="large" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Salary */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <DollarOutlined /> {t("salary") || "Salary"}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={["salary", "min"]} label={t("salary_min")}>
                    <InputNumber
                      min={0}
                      style={{ width: "100%" }}
                      size="large"
                      placeholder="Enter minimum salary"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={["salary", "max"]} label={t("salary_max")}>
                    <InputNumber
                      min={0}
                      style={{ width: "100%" }}
                      size="large"
                      placeholder="Enter maximum salary"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name={["salary", "currency"]}
                    label={t("salary_currency")}
                  >
                    <Select options={currencyOptions} size="large" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={["salary", "period"]}
                    label={t("salary_period")}
                  >
                    <Select options={periodOptions} size="large" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Location */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <EnvironmentOutlined /> {t("location")}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item
                name={["location", "provinceName"]}
                label={t("location")}
              >
                <Input size="large" placeholder="e.g., Ho Chi Minh City" />
              </Form.Item>
              <Form.Item
                name={["location", "detailAddress"]}
                label={t("detail_address")}
              >
                <Input size="large" placeholder="e.g., District 1, Ward 1" />
              </Form.Item>
            </Card>

            {/* Feature & Status */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <SettingOutlined />{" "}
                  {t("other_information") || "Other Information"}
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="featureJob" label={t("featureJob")}>
                    <Switch
                      checkedChildren={tCommon("responses.yes")}
                      unCheckedChildren={tCommon("responses.no")}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="featureDuration"
                    label={t("feature_duration")}
                  >
                    <Select options={featureDurationOptions} size="large" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="isEnableEmailNotification"
                    label={t("isEnableEmailNotification")}
                  >
                    <Switch
                      checkedChildren={tCommon("responses.yes")}
                      unCheckedChildren={tCommon("responses.no")}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="isEnableInformation"
                    label={t("isEnableInformation")}
                  >
                    <Switch
                      checkedChildren={tCommon("responses.yes")}
                      unCheckedChildren={tCommon("responses.no")}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="isAutoAcceptInterviewInvitation"
                    label={t("isAutoAcceptInterviewInvitation")}
                  >
                    <Switch
                      checkedChildren={tCommon("responses.yes")}
                      unCheckedChildren={tCommon("responses.no")}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="isReady" label={t("isReady")}>
                    <Switch
                      checkedChildren={tCommon("responses.yes")}
                      unCheckedChildren={tCommon("responses.no")}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* Save Button */}
        <Card>
          <Row justify="center">
            <Space size="large">
              <Button size="large" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                icon={<SaveOutlined />}
                onClick={() => form.submit()}
                loading={loading}
              >
                {isEdit ? tCommon("actions.update") : tCommon("actions.create")}
              </Button>
            </Space>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default JobSeekerPostFormPage;
