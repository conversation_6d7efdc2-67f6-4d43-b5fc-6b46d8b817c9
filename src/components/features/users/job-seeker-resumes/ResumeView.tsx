// components/features/resumes/ResumeView.tsx
import React from "react";
import {
  Tag,
  Card,
  Row,
  Col,
  Typography,
  Space,
  Avatar,
  Descriptions,
} from "antd";
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  LinkedinOutlined,
  GlobalOutlined,
  BankOutlined,
  BookOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { Resume } from "@/types/resume";
import { formatDate } from "@/constants/resume";

const { Title, Text, Paragraph } = Typography;

interface ResumeViewProps {
  resume: Resume;
  className?: string;
}

const ResumeView: React.FC<ResumeViewProps> = ({ resume, className }) => {
  return (
    <Space
      direction="vertical"
      size="large"
      style={{ width: "100%" }}
      className={className}
    >
      {/* Header Card with Basic Info and Contact */}
      <Card>
        <Row gutter={[24, 24]} align="top">
          {/* Left Side - Avatar + Basic Info */}
          <Col xs={24} lg={12}>
            <Row gutter={[16, 16]} align="middle">
              <Col xs={6} sm={4}>
                <Avatar size={80} icon={<UserOutlined />} />
              </Col>
              <Col xs={18} sm={20}>
                <Space
                  direction="vertical"
                  size="small"
                  style={{ width: "100%" }}
                >
                  <Title level={2} style={{ margin: 0 }}>
                    {resume.name}
                  </Title>
                  <Space>
                    <Tag
                      color={resume.isActive ? "green" : "orange"}
                      style={{ fontSize: "14px", padding: "4px 12px" }}
                    >
                      {resume.isActive ? "Active Resume" : "Inactive"}
                    </Tag>
                  </Space>
                  <Paragraph style={{ margin: 0, fontSize: "16px" }}>
                    {resume.description || "No description provided"}
                  </Paragraph>
                </Space>
              </Col>
            </Row>

            {/* Skills Section */}
            <div style={{ marginTop: 24 }}>
              <Title level={4} style={{ marginBottom: 12 }}>
                <TrophyOutlined /> Skills
              </Title>
              <Space wrap>
                {(resume.skills || []).map((skill, index) => (
                  <Tag
                    key={index}
                    color="blue"
                    style={{ fontSize: "13px", padding: "6px 12px" }}
                  >
                    {skill}
                  </Tag>
                ))}
              </Space>
            </div>
          </Col>

          {/* Right Side - Contact Information */}
          <Col xs={24} lg={12}>
            <div>
              <Title level={4} style={{ marginBottom: 16 }}>
                <UserOutlined /> Contact Information
              </Title>
              {resume.contactInfo ? (
                <Descriptions column={1} bordered size="small">
                  <Descriptions.Item
                    label={
                      <>
                        <PhoneOutlined /> Phone
                      </>
                    }
                  >
                    {resume.contactInfo.phoneNumber ||
                      resume.contactInfo.phone ||
                      "-"}
                  </Descriptions.Item>
                  <Descriptions.Item
                    label={
                      <>
                        <MailOutlined /> Email
                      </>
                    }
                  >
                    {resume.contactInfo.email || "-"}
                  </Descriptions.Item>
                  <Descriptions.Item
                    label={
                      <>
                        <EnvironmentOutlined /> Address
                      </>
                    }
                  >
                    {resume.contactInfo.address || "-"}
                  </Descriptions.Item>
                  <Descriptions.Item
                    label={
                      <>
                        <LinkedinOutlined /> LinkedIn
                      </>
                    }
                  >
                    <a
                      href={resume.contactInfo.linkedInUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {resume.contactInfo.linkedInUrl}
                    </a>
                  </Descriptions.Item>

                  <Descriptions.Item
                    label={
                      <>
                        <GlobalOutlined /> Portfolio
                      </>
                    }
                  >
                    <a
                      href={resume.contactInfo.portfolioUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {resume.contactInfo.portfolioUrl}
                    </a>
                  </Descriptions.Item>
                </Descriptions>
              ) : (
                <Text type="secondary">No contact information provided</Text>
              )}
            </div>
          </Col>
        </Row>
      </Card>

      {/* Work Experience Card */}
      <Card
        title={
          <Title level={4} style={{ margin: 0 }}>
            <BankOutlined /> Work Experience
          </Title>
        }
      >
        {resume.workExperiences && resume.workExperiences.length > 0 ? (
          <Space direction="vertical" size="middle" style={{ width: "100%" }}>
            {resume.workExperiences.map((exp, index) => (
              <Card
                key={index}
                type="inner"
                style={{ backgroundColor: "#fafafa" }}
              >
                <Row gutter={[16, 8]}>
                  <Col span={24}>
                    <Title level={5} style={{ margin: 0, color: "#1890ff" }}>
                      {exp.position}
                    </Title>
                  </Col>
                  <Col span={24}>
                    <Text strong style={{ fontSize: "16px" }}>
                      {exp.company}
                    </Text>
                  </Col>
                  <Col span={24}>
                    <Text type="secondary">
                      <ClockCircleOutlined /> {formatDate(exp.startDate)} -{" "}
                      {exp.endDate ? formatDate(exp.endDate) : "Present"}
                    </Text>
                  </Col>
                  {exp.description && (
                    <Col span={24}>
                      <Paragraph style={{ margin: 0, marginTop: 8 }}>
                        {exp.description}
                      </Paragraph>
                    </Col>
                  )}
                </Row>
              </Card>
            ))}
          </Space>
        ) : (
          <Text type="secondary">No work experience added</Text>
        )}
      </Card>

      {/* Education Card */}
      <Card
        title={
          <Title level={4} style={{ margin: 0 }}>
            <BookOutlined /> Education
          </Title>
        }
      >
        {resume.educations && resume.educations.length > 0 ? (
          <Space direction="vertical" size="middle" style={{ width: "100%" }}>
            {resume.educations.map((edu, index) => (
              <Card
                key={index}
                type="inner"
                style={{ backgroundColor: "#fafafa" }}
              >
                <Row gutter={[16, 8]}>
                  <Col span={24}>
                    <Title level={5} style={{ margin: 0, color: "#52c41a" }}>
                      {edu.degree}
                    </Title>
                  </Col>
                  <Col span={24}>
                    <Text strong style={{ fontSize: "16px" }}>
                      {edu.institution || edu.school}
                    </Text>
                  </Col>
                  <Col span={24}>
                    <Text type="secondary">
                      <ClockCircleOutlined /> {formatDate(edu.startDate)} -{" "}
                      {edu.endDate ? formatDate(edu.endDate) : "Present"}
                    </Text>
                  </Col>
                  {(edu.fieldOfStudy || edu.major) && (
                    <Col span={24}>
                      <Text>
                        <strong>Major:</strong> {edu.fieldOfStudy || edu.major}
                      </Text>
                    </Col>
                  )}
                  {edu.description && (
                    <Col span={24}>
                      <Paragraph style={{ margin: 0, marginTop: 8 }}>
                        {edu.description}
                      </Paragraph>
                    </Col>
                  )}
                </Row>
              </Card>
            ))}
          </Space>
        ) : (
          <Text type="secondary">No education history added</Text>
        )}
      </Card>

      {/* Languages Card */}
      <Card
        title={
          <Title level={4} style={{ margin: 0 }}>
            <GlobalOutlined /> Languages
          </Title>
        }
      >
        {resume.languages && resume.languages.length > 0 ? (
          <Row gutter={[16, 16]}>
            {resume.languages.map((lang, idx) => (
              <Col xs={24} sm={12} md={8} key={idx}>
                <Card
                  type="inner"
                  size="small"
                  style={{ backgroundColor: "#f0f9ff", borderColor: "#91d5ff" }}
                >
                  <Space
                    direction="vertical"
                    size="small"
                    style={{ width: "100%" }}
                  >
                    <Text strong style={{ fontSize: "16px", color: "#1890ff" }}>
                      {lang.language}
                    </Text>
                    <Tag color="cyan">{lang.level}</Tag>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        ) : (
          <Text type="secondary">No language skills added</Text>
        )}
      </Card>

      {/* Part-time Preferences Card */}
      {resume.partTimePreference && (
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <ClockCircleOutlined /> Part-time Preferences
            </Title>
          }
        >
          <Descriptions column={{ xs: 1, sm: 2, md: 2 }} bordered>
            <Descriptions.Item label="Min Hourly Rate" span={1}>
              {resume.partTimePreference.minHourlyRate || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="Max Hours Per Week" span={1}>
              {resume.partTimePreference.maxHoursPerWeek || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="Available Days" span={2}>
              <Space wrap>
                {(resume.partTimePreference.availableDays || []).map(
                  (day, index) => (
                    <Tag key={index} color="green">
                      {day}
                    </Tag>
                  )
                )}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="Available Time Slots" span={2}>
              <Space wrap>
                {(resume.partTimePreference.availableTimeSlots || []).map(
                  (slot, index) => (
                    <Tag key={index} color="blue">
                      {slot}
                    </Tag>
                  )
                )}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="Preferred Job Types" span={2}>
              <Space wrap>
                {(resume.partTimePreference.preferredJobTypes || []).map(
                  (type, index) => (
                    <Tag key={index} color="purple">
                      {type}
                    </Tag>
                  )
                )}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="Remote Only" span={1}>
              <Tag
                color={resume.partTimePreference.remoteOnly ? "green" : "red"}
              >
                {resume.partTimePreference.remoteOnly ? "Yes" : "No"}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Max Travel Distance" span={1}>
              {resume.partTimePreference.maxTravelDistance || "-"}
            </Descriptions.Item>
            {resume.partTimePreference.isStudent !== undefined && (
              <Descriptions.Item label="Student Status" span={1}>
                <Tag
                  color={
                    resume.partTimePreference.isStudent ? "blue" : "default"
                  }
                >
                  {resume.partTimePreference.isStudent
                    ? "Student"
                    : "Not Student"}
                </Tag>
              </Descriptions.Item>
            )}
            {resume.partTimePreference.studyMajor && (
              <Descriptions.Item label="Study Major" span={1}>
                {resume.partTimePreference.studyMajor}
              </Descriptions.Item>
            )}
            {resume.partTimePreference.additionalNotes && (
              <Descriptions.Item label="Additional Notes" span={2}>
                <Paragraph>
                  {resume.partTimePreference.additionalNotes}
                </Paragraph>
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
      )}
    </Space>
  );
};

export default ResumeView;
