// components/features/resumes/ResumeView.tsx
import React from "react";
import { Space } from "antd";
import { Resume } from "@/types/resume";
import {
  Header,
  WorkExperience,
  Education,
  Languages,
  PartTimePreferences,
} from "./components";

interface ResumeViewProps {
  resume: Resume;
  className?: string;
}

const ResumeView: React.FC<ResumeViewProps> = ({ resume, className }) => {
  return (
    <Space
      direction="vertical"
      size="large"
      style={{ width: "100%" }}
      className={className}
    >
      {/* Header with Basic Info and Contact */}
      <Header
        basicInfo={{
          name: resume.name,
          isActive: resume.isActive,
          description: resume.description,
        }}
        contactInfo={resume.contactInfo}
        skills={resume.skills}
      />

      {/* Work Experience */}
      <WorkExperience workExperiences={resume.workExperiences} />

      {/* Education */}
      <Education educations={resume.educations} />

      {/* Languages */}
      <Languages languages={resume.languages} />

      {/* Part-time Preferences */}
      <PartTimePreferences partTimePreference={resume.partTimePreference} />
    </Space>
  );
};

export default ResumeView;
