// components/features/resumes/ResumeView.tsx
import React from "react";
import { Space } from "antd";
import { Resume } from "@/types/resume";
import {
  ResumeHeader,
  ResumeWorkExperience,
  ResumeEducation,
  ResumeLanguages,
  ResumePartTimePreferences,
} from "./components";

interface ResumeViewProps {
  resume: Resume;
  className?: string;
}

const ResumeView: React.FC<ResumeViewProps> = ({ resume, className }) => {
  return (
    <Space
      direction="vertical"
      size="large"
      style={{ width: "100%" }}
      className={className}
    >
      {/* Header with Basic Info and Contact */}
      <ResumeHeader resume={resume} />

      {/* Work Experience */}
      <ResumeWorkExperience workExperiences={resume.workExperiences} />

      {/* Education */}
      <ResumeEducation educations={resume.educations} />

      {/* Languages */}
      <ResumeLanguages languages={resume.languages} />

      {/* Part-time Preferences */}
      <ResumePartTimePreferences
        partTimePreference={resume.partTimePreference}
      />
    </Space>
  );
};

export default ResumeView;
