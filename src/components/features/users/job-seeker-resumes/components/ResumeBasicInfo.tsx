import React from "react";
import {
  Tag,
  Row,
  Col,
  Typography,
  Space,
  Avatar,
} from "antd";
import {
  UserOutlined,
} from "@ant-design/icons";
import { Resume } from "@/types/resume";

const { Title, Paragraph } = Typography;

interface ResumeBasicInfoProps {
  resume: Resume;
  showAvatar?: boolean;
}

const ResumeBasicInfo: React.FC<ResumeBasicInfoProps> = ({ 
  resume, 
  showAvatar = true 
}) => {
  return (
    <Row gutter={[16, 16]} align="middle">
      {showAvatar && (
        <Col xs={6} sm={4}>
          <Avatar size={80} icon={<UserOutlined />} />
        </Col>
      )}
      <Col xs={showAvatar ? 18 : 24} sm={showAvatar ? 20 : 24}>
        <Space
          direction="vertical"
          size="small"
          style={{ width: "100%" }}
        >
          <Title level={2} style={{ margin: 0 }}>
            {resume.name}
          </Title>
          <Space>
            <Tag
              color={resume.isActive ? "green" : "orange"}
              style={{ fontSize: "14px", padding: "4px 12px" }}
            >
              {resume.isActive ? "Active Resume" : "Inactive"}
            </Tag>
          </Space>
          <Paragraph style={{ margin: 0, fontSize: "16px" }}>
            {resume.description || "No description provided"}
          </Paragraph>
        </Space>
      </Col>
    </Row>
  );
};

export default ResumeBasicInfo;
