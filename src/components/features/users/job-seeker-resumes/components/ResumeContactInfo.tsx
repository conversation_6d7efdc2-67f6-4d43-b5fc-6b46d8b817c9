import React from "react";
import { Typography, Descriptions } from "antd";
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  LinkedinOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import { Resume } from "@/types/resume";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;

interface ResumeContactInfoProps {
  contactInfo: Resume["contactInfo"];
  showTitle?: boolean;
}

const ResumeContactInfo: React.FC<ResumeContactInfoProps> = ({
  contactInfo,
  showTitle = true,
}) => {
  const t = useTranslations("resume");

  return (
    <div>
      {showTitle && (
        <Title level={4} style={{ marginBottom: 16 }}>
          <UserOutlined /> {t("contact_information")}
        </Title>
      )}
      {contactInfo ? (
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item
            label={
              <>
                <PhoneOutlined /> {t("phone")}
              </>
            }
          >
            {contactInfo.phoneNumber || contactInfo.phone || "-"}
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <MailOutlined /> {t("email")}
              </>
            }
          >
            {contactInfo.email || "-"}
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <EnvironmentOutlined /> {t("address")}
              </>
            }
          >
            {contactInfo.address || "-"}
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <LinkedinOutlined /> {t("linkedin")}
              </>
            }
          >
            <a
              href={contactInfo.linkedInUrl}
              target="_blank"
              rel="noopener noreferrer"
            >
              {contactInfo.linkedInUrl}
            </a>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <>
                <GlobalOutlined /> {t("portfolio")}
              </>
            }
          >
            <a
              href={contactInfo.portfolioUrl}
              target="_blank"
              rel="noopener noreferrer"
            >
              {contactInfo.portfolioUrl}
            </a>
          </Descriptions.Item>
        </Descriptions>
      ) : (
        <Text type="secondary">{t("no_contact_info")}</Text>
      )}
    </div>
  );
};

export default ResumeContactInfo;
