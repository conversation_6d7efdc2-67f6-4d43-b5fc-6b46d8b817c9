import React from "react";
import {
  Typography,
  Descriptions,
} from "antd";
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  LinkedinOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import { Resume } from "@/types/resume";

const { Title, Text } = Typography;

interface ResumeContactInfoProps {
  contactInfo: Resume["contactInfo"];
  showTitle?: boolean;
}

const ResumeContactInfo: React.FC<ResumeContactInfoProps> = ({ 
  contactInfo, 
  showTitle = true 
}) => {
  return (
    <div>
      {showTitle && (
        <Title level={4} style={{ marginBottom: 16 }}>
          <UserOutlined /> Contact Information
        </Title>
      )}
      {contactInfo ? (
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item
            label={
              <>
                <PhoneOutlined /> Phone
              </>
            }
          >
            {contactInfo.phoneNumber ||
              contactInfo.phone ||
              "-"}
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <MailOutlined /> Email
              </>
            }
          >
            {contactInfo.email || "-"}
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <EnvironmentOutlined /> Address
              </>
            }
          >
            {contactInfo.address || "-"}
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <>
                <LinkedinOutlined /> LinkedIn
              </>
            }
          >
            <a
              href={contactInfo.linkedInUrl}
              target="_blank"
              rel="noopener noreferrer"
            >
              {contactInfo.linkedInUrl}
            </a>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <>
                <GlobalOutlined /> Portfolio
              </>
            }
          >
            <a
              href={contactInfo.portfolioUrl}
              target="_blank"
              rel="noopener noreferrer"
            >
              {contactInfo.portfolioUrl}
            </a>
          </Descriptions.Item>
        </Descriptions>
      ) : (
        <Text type="secondary">No contact information provided</Text>
      )}
    </div>
  );
};

export default ResumeContactInfo;
