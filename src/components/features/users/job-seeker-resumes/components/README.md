# Resume Components

This directory contains reusable components for displaying resume information. These components have been extracted from the original `ResumeView` component to allow for better modularity and reusability.

## Main Components

### ResumeHeader
Complete header section with basic info, contact info, and skills.
```tsx
import { ResumeHeader } from './components';

<ResumeHeader resume={resume} />
```

### ResumeWorkExperience
Displays work experience section.
```tsx
import { ResumeWorkExperience } from './components';

<ResumeWorkExperience workExperiences={resume.workExperiences} />
```

### ResumeEducation
Displays education section.
```tsx
import { ResumeEducation } from './components';

<ResumeEducation educations={resume.educations} />
```

### ResumeLanguages
Displays languages section.
```tsx
import { ResumeLanguages } from './components';

<ResumeLanguages languages={resume.languages} />
```

### ResumePartTimePreferences
Displays part-time preferences section (only shows if data exists).
```tsx
import { ResumePartTimePreferences } from './components';

<ResumePartTimePreferences partTimePreference={resume.partTimePreference} />
```

## Sub-Components (for granular reuse)

### ResumeBasicInfo
Basic information with name, status, and description.
```tsx
import { ResumeBasicInfo } from './components';

<ResumeBasicInfo resume={resume} showAvatar={true} />
```

### ResumeContactInfo
Contact information section.
```tsx
import { ResumeContactInfo } from './components';

<ResumeContactInfo contactInfo={resume.contactInfo} showTitle={true} />
```

### ResumeSkills
Skills section that can be displayed as inline or as a card.
```tsx
import { ResumeSkills } from './components';

<ResumeSkills skills={resume.skills} showAsCard={false} />
```

## Usage Examples

### Full Resume View
```tsx
import {
  ResumeHeader,
  ResumeWorkExperience,
  ResumeEducation,
  ResumeLanguages,
  ResumePartTimePreferences,
} from './components';

const MyResumeView = ({ resume }) => (
  <Space direction="vertical" size="large">
    <ResumeHeader resume={resume} />
    <ResumeWorkExperience workExperiences={resume.workExperiences} />
    <ResumeEducation educations={resume.educations} />
    <ResumeLanguages languages={resume.languages} />
    <ResumePartTimePreferences partTimePreference={resume.partTimePreference} />
  </Space>
);
```

### Custom Layout with Sub-Components
```tsx
import {
  ResumeBasicInfo,
  ResumeContactInfo,
  ResumeSkills,
} from './components';

const CompactResumeCard = ({ resume }) => (
  <Card>
    <Row gutter={16}>
      <Col span={12}>
        <ResumeBasicInfo resume={resume} showAvatar={false} />
      </Col>
      <Col span={12}>
        <ResumeContactInfo contactInfo={resume.contactInfo} showTitle={false} />
      </Col>
    </Row>
    <ResumeSkills skills={resume.skills} showAsCard={false} />
  </Card>
);
```

### Skills Only Component
```tsx
import { ResumeSkills } from './components';

const SkillsCard = ({ skills }) => (
  <ResumeSkills skills={skills} showAsCard={true} />
);
```

## Props Reference

### ResumeHeader
- `resume: Resume` - Complete resume object

### ResumeWorkExperience
- `workExperiences: Resume["workExperiences"]` - Array of work experiences

### ResumeEducation
- `educations: Resume["educations"]` - Array of education entries

### ResumeLanguages
- `languages: Resume["languages"]` - Array of language skills

### ResumePartTimePreferences
- `partTimePreference: Resume["partTimePreference"]` - Part-time preferences object

### ResumeBasicInfo
- `resume: Resume` - Resume object
- `showAvatar?: boolean` - Whether to show avatar (default: true)

### ResumeContactInfo
- `contactInfo: Resume["contactInfo"]` - Contact information object
- `showTitle?: boolean` - Whether to show section title (default: true)

### ResumeSkills
- `skills: Resume["skills"]` - Array of skills
- `showAsCard?: boolean` - Whether to wrap in Card component (default: false)
