import React from "react";
import { Tag, Card, Typography, Space, Descriptions } from "antd";
import { ClockCircleOutlined } from "@ant-design/icons";
import { Resume } from "@/types/resume";
import { useTranslations } from "next-intl";

const { Title, Paragraph } = Typography;

interface ResumePartTimePreferencesProps {
  partTimePreference: Resume["partTimePreference"];
}

const ResumePartTimePreferences: React.FC<ResumePartTimePreferencesProps> = ({
  partTimePreference,
}) => {
  const t = useTranslations("resume");

  if (!partTimePreference) {
    return null;
  }

  return (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <ClockCircleOutlined /> {t("part_time_preferences")}
        </Title>
      }
    >
      <Descriptions column={{ xs: 1, sm: 2, md: 2 }} bordered>
        <Descriptions.Item label={t("min_hourly_rate")} span={1}>
          {partTimePreference.minHourlyRate || "-"}
        </Descriptions.Item>
        <Descriptions.Item label={t("max_hours_per_week")} span={1}>
          {partTimePreference.maxHoursPerWeek || "-"}
        </Descriptions.Item>
        <Descriptions.Item label={t("available_days")} span={2}>
          <Space wrap>
            {(partTimePreference.availableDays || []).map((day, index) => (
              <Tag key={index} color="green">
                {day}
              </Tag>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label={t("available_time_slots")} span={2}>
          <Space wrap>
            {(partTimePreference.availableTimeSlots || []).map(
              (slot, index) => (
                <Tag key={index} color="blue">
                  {slot}
                </Tag>
              )
            )}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label={t("preferred_job_types")} span={2}>
          <Space wrap>
            {(partTimePreference.preferredJobTypes || []).map((type, index) => (
              <Tag key={index} color="purple">
                {type}
              </Tag>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label={t("remote_only")} span={1}>
          <Tag color={partTimePreference.remoteOnly ? "green" : "red"}>
            {partTimePreference.remoteOnly ? t("yes") : t("no")}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label={t("max_travel_distance")} span={1}>
          {partTimePreference.maxTravelDistance || "-"}
        </Descriptions.Item>
        {partTimePreference.isStudent !== undefined && (
          <Descriptions.Item label={t("student_status")} span={1}>
            <Tag color={partTimePreference.isStudent ? "blue" : "default"}>
              {partTimePreference.isStudent ? t("student") : t("not_student")}
            </Tag>
          </Descriptions.Item>
        )}
        {partTimePreference.studyMajor && (
          <Descriptions.Item label={t("study_major")} span={1}>
            {partTimePreference.studyMajor}
          </Descriptions.Item>
        )}
        {partTimePreference.additionalNotes && (
          <Descriptions.Item label={t("additional_notes")} span={2}>
            <Paragraph>{partTimePreference.additionalNotes}</Paragraph>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Card>
  );
};

export default ResumePartTimePreferences;
