import React from "react";
import {
  Tag,
  Card,
  Typography,
  Space,
  Descriptions,
} from "antd";
import {
  ClockCircleOutlined,
} from "@ant-design/icons";
import { Resume } from "@/types/resume";

const { Title, Paragraph } = Typography;

interface ResumePartTimePreferencesProps {
  partTimePreference: Resume["partTimePreference"];
}

const ResumePartTimePreferences: React.FC<ResumePartTimePreferencesProps> = ({ 
  partTimePreference 
}) => {
  if (!partTimePreference) {
    return null;
  }

  return (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <ClockCircleOutlined /> Part-time Preferences
        </Title>
      }
    >
      <Descriptions column={{ xs: 1, sm: 2, md: 2 }} bordered>
        <Descriptions.Item label="Min Hourly Rate" span={1}>
          {partTimePreference.minHourlyRate || "-"}
        </Descriptions.Item>
        <Descriptions.Item label="Max Hours Per Week" span={1}>
          {partTimePreference.maxHoursPerWeek || "-"}
        </Descriptions.Item>
        <Descriptions.Item label="Available Days" span={2}>
          <Space wrap>
            {(partTimePreference.availableDays || []).map(
              (day, index) => (
                <Tag key={index} color="green">
                  {day}
                </Tag>
              )
            )}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="Available Time Slots" span={2}>
          <Space wrap>
            {(partTimePreference.availableTimeSlots || []).map(
              (slot, index) => (
                <Tag key={index} color="blue">
                  {slot}
                </Tag>
              )
            )}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="Preferred Job Types" span={2}>
          <Space wrap>
            {(partTimePreference.preferredJobTypes || []).map(
              (type, index) => (
                <Tag key={index} color="purple">
                  {type}
                </Tag>
              )
            )}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="Remote Only" span={1}>
          <Tag
            color={partTimePreference.remoteOnly ? "green" : "red"}
          >
            {partTimePreference.remoteOnly ? "Yes" : "No"}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Max Travel Distance" span={1}>
          {partTimePreference.maxTravelDistance || "-"}
        </Descriptions.Item>
        {partTimePreference.isStudent !== undefined && (
          <Descriptions.Item label="Student Status" span={1}>
            <Tag
              color={
                partTimePreference.isStudent ? "blue" : "default"
              }
            >
              {partTimePreference.isStudent
                ? "Student"
                : "Not Student"}
            </Tag>
          </Descriptions.Item>
        )}
        {partTimePreference.studyMajor && (
          <Descriptions.Item label="Study Major" span={1}>
            {partTimePreference.studyMajor}
          </Descriptions.Item>
        )}
        {partTimePreference.additionalNotes && (
          <Descriptions.Item label="Additional Notes" span={2}>
            <Paragraph>
              {partTimePreference.additionalNotes}
            </Paragraph>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Card>
  );
};

export default ResumePartTimePreferences;
