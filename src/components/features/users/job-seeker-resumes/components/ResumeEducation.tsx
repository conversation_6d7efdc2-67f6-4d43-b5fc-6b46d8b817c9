import React from "react";
import { <PERSON>, <PERSON>, <PERSON>, Typo<PERSON>, Space } from "antd";
import { BookOutlined, ClockCircleOutlined } from "@ant-design/icons";
import { Resume } from "@/types/resume";
import { formatDate } from "@/constants/resume";
import { useTranslations } from "next-intl";

const { Title, Text, Paragraph } = Typography;

interface ResumeEducationProps {
  educations: Resume["educations"];
}

const ResumeEducation: React.FC<ResumeEducationProps> = ({ educations }) => {
  const t = useTranslations("resume");

  return (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <BookOutlined /> {t("education")}
        </Title>
      }
    >
      {educations && educations.length > 0 ? (
        <Space direction="vertical" size="middle" style={{ width: "100%" }}>
          {educations.map((edu, index) => (
            <Card
              key={index}
              type="inner"
              style={{ backgroundColor: "#fafafa" }}
            >
              <Row gutter={[16, 8]}>
                <Col span={24}>
                  <Title level={5} style={{ margin: 0, color: "#52c41a" }}>
                    {edu.degree}
                  </Title>
                </Col>
                <Col span={24}>
                  <Text strong style={{ fontSize: "16px" }}>
                    {edu.institution || edu.school}
                  </Text>
                </Col>
                <Col span={24}>
                  <Text type="secondary">
                    <ClockCircleOutlined /> {formatDate(edu.startDate)} -{" "}
                    {edu.endDate ? formatDate(edu.endDate) : t("present")}
                  </Text>
                </Col>
                {(edu.fieldOfStudy || edu.major) && (
                  <Col span={24}>
                    <Text>
                      <strong>{t("major")}:</strong>{" "}
                      {edu.fieldOfStudy || edu.major}
                    </Text>
                  </Col>
                )}
                {edu.description && (
                  <Col span={24}>
                    <Paragraph style={{ margin: 0, marginTop: 8 }}>
                      {edu.description}
                    </Paragraph>
                  </Col>
                )}
              </Row>
            </Card>
          ))}
        </Space>
      ) : (
        <Text type="secondary">{t("no_education")}</Text>
      )}
    </Card>
  );
};

export default ResumeEducation;
