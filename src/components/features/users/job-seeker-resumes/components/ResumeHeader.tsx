import React from "react";
import { <PERSON>, <PERSON>, Col } from "antd";
import { Resume } from "@/types/resume";
import ResumeBasicInfo from "./ResumeBasicInfo";
import ResumeContactInfo from "./ResumeContactInfo";
import ResumeSkills from "./ResumeSkills";

interface ResumeHeaderProps {
  resume: Resume;
}

const ResumeHeader: React.FC<ResumeHeaderProps> = ({ resume }) => {
  return (
    <Card>
      <Row gutter={[24, 24]} align="top">
        {/* Left Side - Avatar + Basic Info */}
        <Col xs={24} lg={12}>
          <ResumeBasicInfo resume={resume} />
          <ResumeSkills skills={resume.skills} />
        </Col>

        {/* Right Side - Contact Information */}
        <Col xs={24} lg={12}>
          <ResumeContactInfo contactInfo={resume.contactInfo} />
        </Col>
      </Row>
    </Card>
  );
};

export default ResumeHeader;
