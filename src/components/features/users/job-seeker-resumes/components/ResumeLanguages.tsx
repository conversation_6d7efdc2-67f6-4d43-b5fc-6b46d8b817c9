import React from "react";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, Typo<PERSON>, Space } from "antd";
import { GlobalOutlined } from "@ant-design/icons";
import { Resume } from "@/types/resume";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;

interface ResumeLanguagesProps {
  languages: Resume["languages"];
}

const ResumeLanguages: React.FC<ResumeLanguagesProps> = ({ languages }) => {
  const t = useTranslations("resume");

  return (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <GlobalOutlined /> {t("languages")}
        </Title>
      }
    >
      {languages && languages.length > 0 ? (
        <Row gutter={[16, 16]}>
          {languages.map((lang, idx) => (
            <Col xs={24} sm={12} md={8} key={idx}>
              <Card
                type="inner"
                size="small"
                style={{ backgroundColor: "#f0f9ff", borderColor: "#91d5ff" }}
              >
                <Space
                  direction="vertical"
                  size="small"
                  style={{ width: "100%" }}
                >
                  <Text strong style={{ fontSize: "16px", color: "#1890ff" }}>
                    {lang.language}
                  </Text>
                  <Tag color="cyan">{lang.level}</Tag>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      ) : (
        <Text type="secondary">{t("no_languages")}</Text>
      )}
    </Card>
  );
};

export default ResumeLanguages;
