import React from "react";
import {
  Tag,
  Card,
  Typography,
  Space,
} from "antd";
import {
  TrophyOutlined,
} from "@ant-design/icons";
import { Resume } from "@/types/resume";

const { Title, Text } = Typography;

interface ResumeSkillsProps {
  skills: Resume["skills"];
  showAsCard?: boolean;
}

const ResumeSkills: React.FC<ResumeSkillsProps> = ({ 
  skills, 
  showAsCard = false 
}) => {
  const skillsContent = (
    <>
      <Title level={4} style={{ marginBottom: 12 }}>
        <TrophyOutlined /> Skills
      </Title>
      {skills && skills.length > 0 ? (
        <Space wrap>
          {skills.map((skill, index) => (
            <Tag
              key={index}
              color="blue"
              style={{ fontSize: "13px", padding: "6px 12px" }}
            >
              {skill}
            </Tag>
          ))}
        </Space>
      ) : (
        <Text type="secondary">No skills added</Text>
      )}
    </>
  );

  if (showAsCard) {
    return (
      <Card>
        {skillsContent}
      </Card>
    );
  }

  return (
    <div style={{ marginTop: 24 }}>
      {skillsContent}
    </div>
  );
};

export default ResumeSkills;
