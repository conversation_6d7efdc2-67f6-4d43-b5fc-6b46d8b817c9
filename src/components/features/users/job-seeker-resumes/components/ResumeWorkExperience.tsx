import React from "react";
import { <PERSON>, <PERSON>, <PERSON>, Typo<PERSON>, Space } from "antd";
import { BankOutlined, ClockCircleOutlined } from "@ant-design/icons";
import { Resume } from "@/types/resume";
import { formatDate } from "@/constants/resume";
import { useTranslations } from "next-intl";

const { Title, Text, Paragraph } = Typography;

interface ResumeWorkExperienceProps {
  workExperiences: Resume["workExperiences"];
}

const ResumeWorkExperience: React.FC<ResumeWorkExperienceProps> = ({
  workExperiences,
}) => {
  const t = useTranslations("resume");

  return (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <BankOutlined /> {t("work_experience")}
        </Title>
      }
    >
      {workExperiences && workExperiences.length > 0 ? (
        <Space direction="vertical" size="middle" style={{ width: "100%" }}>
          {workExperiences.map((exp, index) => (
            <Card
              key={index}
              type="inner"
              style={{ backgroundColor: "#fafafa" }}
            >
              <Row gutter={[16, 8]}>
                <Col span={24}>
                  <Title level={5} style={{ margin: 0, color: "#1890ff" }}>
                    {exp.position}
                  </Title>
                </Col>
                <Col span={24}>
                  <Text strong style={{ fontSize: "16px" }}>
                    {exp.company}
                  </Text>
                </Col>
                <Col span={24}>
                  <Text type="secondary">
                    <ClockCircleOutlined /> {formatDate(exp.startDate)} -{" "}
                    {exp.endDate ? formatDate(exp.endDate) : t("present")}
                  </Text>
                </Col>
                {exp.description && (
                  <Col span={24}>
                    <Paragraph style={{ margin: 0, marginTop: 8 }}>
                      {exp.description}
                    </Paragraph>
                  </Col>
                )}
              </Row>
            </Card>
          ))}
        </Space>
      ) : (
        <Text type="secondary">{t("no_work_experience")}</Text>
      )}
    </Card>
  );
};

export default ResumeWorkExperience;
