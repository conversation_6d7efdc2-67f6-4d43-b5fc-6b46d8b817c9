"use client";
import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseDatePicker from "@/components/ui/datepickers/BaseDatePicker";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import {
  INTERVIEW_DURATION_OPTIONS,
  INTERVIEW_STATUS_OPTIONS,
  INTERVIEW_TYPE_OPTIONS,
} from "@/constants/interview";
import { UserRole } from "@/constants/userRole";
import ApiService from "@/services/ApiService";
import InterviewService from "@/services/interviewService";
import {
  Interview,
  InterviewFormData,
  InterviewStatus,
  InterviewType,
} from "@/types/interview";
import { Card, Col, Divider, Form, message, Row } from "antd";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";

interface InterviewFormProps {
  interview?: Interview;
  onSuccess: () => void;
  onCancel: () => void;
}

interface JobPost {
  id: string;
  title: string;
}

interface User {
  id: string;
  name: string;
  role: UserRole;
}

const InterviewForm: React.FC<InterviewFormProps> = ({
  interview,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [interviewType, setInterviewType] = useState<InterviewType>(
    interview?.type || InterviewType.VIDEO_CALL
  );
  const [jobPosts, setJobPosts] = useState<{ value: string; label: string }[]>(
    []
  );
  const [candidates, setCandidates] = useState<
    { value: string; label: string }[]
  >([]);
  const [employers, setEmployers] = useState<
    { value: string; label: string }[]
  >([]);

  // Fetch job posts, candidates, and employers for dropdown options
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        // Fetch job posts
        const jobPostsResponse = await ApiService.get<{ data: JobPost[] }>(
          "/api/posts",
          { limit: 100 }
        );
        if (jobPostsResponse.data.data) {
          setJobPosts(
            jobPostsResponse.data.data.map((post: JobPost) => ({
              value: post.id,
              label: post.title,
            }))
          );
        }

        // Fetch candidates (job seekers)
        const candidatesResponse = await ApiService.get<{ data: User[] }>(
          "/api/users",
          { role: UserRole.JOB_SEEKER, limit: 100 }
        );
        if (candidatesResponse.data.data) {
          setCandidates(
            candidatesResponse.data.data.map((user: User) => ({
              value: user.id,
              label: user.name,
            }))
          );
        }

        // Fetch employers
        const employersResponse = await ApiService.get<{ data: User[] }>(
          "/api/users",
          { role: UserRole.EMPLOYER, limit: 100 }
        );
        if (employersResponse.data.data) {
          setEmployers(
            employersResponse.data.data.map((user: User) => ({
              value: user.id,
              label: user.name,
            }))
          );
        }
      } catch (error) {
        console.error("Failed to fetch options:", error);
        message.error("Failed to load form options");

        // For demonstration purposes, add some mock data if API fails
        setJobPosts([
          { value: "JP-2024-001", label: "Front-end Developer" },
          { value: "JP-2024-002", label: "UX/UI Designer" },
          { value: "JP-2024-003", label: "Marketing Assistant" },
        ]);

        setCandidates([
          { value: "CAND-201", label: "Nguyễn Văn A" },
          { value: "CAND-202", label: "Trần Thị B" },
          { value: "CAND-203", label: "Lê Văn C" },
        ]);

        setEmployers([
          { value: "EMP-101", label: "Tech Innovations" },
          { value: "EMP-102", label: "Creative Solutions" },
          { value: "EMP-103", label: "Marketing Group" },
        ]);
      }
    };

    fetchOptions();
  }, []);

  // Set form initial values when interview data is available
  useEffect(() => {
    if (interview) {
      form.setFieldsValue({
        ...interview,
        scheduledDate: interview.scheduledDate
          ? dayjs(interview.scheduledDate)
          : undefined,
      });
      setInterviewType(interview.type);
    }
  }, [interview, form]);

  const handleTypeChange = (value: InterviewType) => {
    setInterviewType(value);
  };

  const handleSubmit = async (values: Interview) => {
    try {
      setLoading(true);

      // Format the date
      const formattedValues = {
        ...values,
        scheduledDate: values.scheduledDate ? values.scheduledDate : undefined,
      };

      if (interview) {
        // Update existing interview
        await InterviewService.updateInterview(interview.id, formattedValues);
        message.success("Interview updated successfully");
      } else {
        // Create new interview
        await InterviewService.createInterview(
          formattedValues as InterviewFormData
        );
        message.success("Interview scheduled successfully");
      }

      onSuccess();
    } catch (error) {
      console.error("Failed to save interview:", error);
      message.error("Failed to save interview");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          status: InterviewStatus.SCHEDULED,
          type: InterviewType.VIDEO_CALL,
          duration: 30,
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <BaseSelect
              label="Job Post"
              name="jobPostId"
              required
              options={jobPosts}
              placeholder="Select job post"
            />
          </Col>

          <Col span={12}>
            <BaseSelect
              label="Candidate"
              name="candidateId"
              required
              options={candidates}
              placeholder="Select candidate"
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <BaseSelect
              label="Employer"
              name="employerId"
              required
              options={employers}
              placeholder="Select employer"
            />
          </Col>

          <Col span={12}>
            <BaseSelect
              label="Status"
              name="status"
              required
              options={INTERVIEW_STATUS_OPTIONS.map((status) => ({
                value: status.value,
                label: status.label,
              }))}
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <BaseDatePicker
              label="Scheduled Date & Time"
              name="scheduledDate"
              required
              showTime
              format="YYYY-MM-DD HH:mm"
              placeholder="Select date and time"
            />
          </Col>

          <Col span={12}>
            <BaseSelect
              label="Duration (minutes)"
              name="duration"
              required
              options={INTERVIEW_DURATION_OPTIONS.map((option) => ({
                value: option.value,
                label: option.label,
              }))}
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <BaseSelect
              label="Interview Type"
              name="type"
              required
              options={INTERVIEW_TYPE_OPTIONS.map((type) => ({
                value: type.value,
                label: type.label,
              }))}
              onChange={handleTypeChange}
            />
          </Col>

          <Col span={12}>
            {interviewType === InterviewType.IN_PERSON && (
              <BaseInput
                label="Location"
                name="location"
                required
                placeholder="Enter interview location"
              />
            )}

            {interviewType === InterviewType.VIDEO_CALL && (
              <BaseInput
                label="Meeting Link"
                name="meetingLink"
                required
                placeholder="Enter video conference link"
              />
            )}

            {interviewType === InterviewType.PHONE_CALL && (
              <BaseInput
                label="Contact Number"
                name="contactNumber"
                placeholder="Enter contact phone number"
              />
            )}
          </Col>
        </Row>

        <Divider />

        <BaseTextArea
          label="Notes"
          name="notes"
          placeholder="Enter any additional notes for this interview"
          rows={4}
          helpText="Include preparation instructions, required documents, or any other important information."
        />

        <div className="flex justify-end gap-2 mt-4">
          <BaseButton onClick={onCancel} label="Cancel" />
          <BaseButton
            type="primary"
            htmlType="submit"
            loading={loading}
            label={interview ? "Update Interview" : "Schedule Interview"}
          />
        </div>
      </Form>
    </Card>
  );
};

export default InterviewForm;
