"use client";
import BaseTable from "@/components/ui/tables/BaseTable";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import ContractService from "@/services/contractService";
import { Contract, contractStatusOptions } from "@/types/contract";
import { Tag } from "antd";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import React from "react";
import { UserRole } from "@/constants/userRole";

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};

interface ContractsListProps {
  userRole?: UserRole;
}

const ContractsList: React.FC<ContractsListProps> = ({
  userRole = UserRole.ADMIN,
}) => {
  const router = useRouter();

  const getStatusColor = (status: Contract["status"]) => {
    const statusColors: Record<Contract["status"], string> = {
      draft: "default",
      offered: "processing",
      active: "success",
      completed: "blue",
      terminated: "error",
    };
    return statusColors[status];
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: "Title",
      dataIndex: "title",
      key: "title",
      render: (text: string) => <span className="font-medium">{text}</span>,
    },
    {
      title: "Employer",
      dataIndex: "employerName",
      key: "employerName",
      render: (text: string) => (userRole !== UserRole.EMPLOYER ? text : null),
      width: 150,
    },
    {
      title: "Job Seeker",
      dataIndex: "jobSeekerName",
      key: "jobSeekerName",
      render: (text: string) =>
        userRole !== UserRole.JOB_SEEKER ? text : null,
      width: 150,
    },
    {
      title: "Contract Type",
      dataIndex: "contractType",
      key: "contractType",
      render: (text: string) => (
        <span className="capitalize">{text.replace("-", " ")}</span>
      ),
      width: 140,
    },
    {
      title: "Hourly Rate",
      dataIndex: "hourlyRate",
      key: "hourlyRate",
      render: (rate: number) => formatCurrency(rate),
      width: 120,
    },
    {
      title: "Duration",
      key: "duration",
      render: (_: unknown, record: Contract) => (
        <span>
          {dayjs(record.startDate).format("MMM D, YYYY")} -
          {dayjs(record.endDate).format("MMM D, YYYY")}
        </span>
      ),
      width: 200,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: Contract["status"]) => (
        <Tag color={getStatusColor(status)} className="capitalize">
          {status}
        </Tag>
      ),
      width: 100,
    },
    {
      title: "Created",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date: Date) => dayjs(date).format("MMM D, YYYY"),
      width: 120,
    },
  ];

  // Filter columns based on user role
  const filteredColumns = columns.filter((col) => {
    if (userRole === UserRole.EMPLOYER && col.key === "employerName")
      return false;
    if (userRole === UserRole.JOB_SEEKER && col.key === "jobSeekerName")
      return false;
    return true;
  });

  const filters: FilterConfig[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: contractStatusOptions,
    },
    {
      key: "contractType",
      label: "Contract Type",
      type: "select",
      options: [
        { value: "part-time", label: "Part-time" },
        { value: "freelance", label: "Freelance" },
        { value: "project", label: "Project" },
      ],
    },
    {
      key: "dateRange",
      label: "Created Date",
      type: "dateRange",
    },
  ];

  const handleEdit = (contract: Contract) => {
    if (userRole === UserRole.EMPLOYER) {
      router.push(`/employers-management/contracts/${contract.id}/edit`);
    } else {
      router.push(`/job-seekers-management/contracts/${contract.id}/edit`);
    }
  };

  const handleCreateContract = () => {
    if (userRole === UserRole.EMPLOYER) {
      router.push("/employers-management/contracts/create");
    } else {
      router.push("/job-seekers-management/contracts/create");
    }
  };

  const handleDeleteContract = async (contract: Contract) => {
    try {
      await ContractService.deleteContract(contract.id);
      return Promise.resolve();
    } catch (error) {
      console.error("Failed to delete contract:", error);
      return Promise.reject(error);
    }
  };

  const handleRowClick = (record: Contract) => {
    if (userRole === UserRole.EMPLOYER) {
      router.push(`/employers-management/contracts/${record.id}`);
    } else {
      router.push(`/job-seekers-management/contracts/${record.id}`);
    }
  };

  return (
    <BaseTable
      api={ContractService.getContracts}
      columns={filteredColumns}
      rowKey="id"
      title="Contracts"
      createBtnText="Create Contract"
      showSearch={true}
      searchPlaceholder="Search by title or ID..."
      onCreate={handleCreateContract}
      onDelete={handleDeleteContract}
      onEdit={handleEdit}
      onRowClick={handleRowClick}
      filters={filters}
    />
  );
};

export default ContractsList;
