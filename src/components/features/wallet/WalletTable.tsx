"use client";

import BaseTable, { BaseTableRef } from "@/components/ui/tables/BaseTable";
import { Wallet } from "@/types/wallet";
import { Avatar, Space, Typography, Tag } from "antd";
import { useTranslations } from "next-intl";
import { forwardRef, useImperativeHandle, useRef } from "react";

const { Text } = Typography;

interface WalletTableProps {
  api: (params?: Record<string, unknown>) => Promise<any>;
  tTitle: string;
}

const WalletTable = forwardRef<BaseTableRef, WalletTableProps>(
  ({ api, tTitle }, ref) => {
    const tCommon = useTranslations("common");
    const tWallet = useTranslations("wallet");
    const tableRef = useRef<BaseTableRef>(null);

    useImperativeHandle(ref, () => ({
      refetch: () => tableRef.current?.refetch(),
    }));

    const columns = [
      {
        title: tCommon("fields.id"),
        dataIndex: "id",
        key: "id",
        width: 80,
      },
      {
        title: tC<PERSON>mon("fields.user"),
        key: "user",
        render: (_: unknown, record: Wallet) => (
          <Space>
            <Avatar
              src={record.user.profilePicture || "/user/default-avatar.svg"}
              alt={record.user.name}
              size="large"
            >
              {record.user.name ? record.user.name[0].toUpperCase() : "U"}
            </Avatar>
            <div>
              <Text strong>{record.user.name}</Text>
              <div>
                <Text type="secondary">{record.user.email}</Text>
              </div>
              <div>
                <Text type="secondary">{record.user.phoneNumber}</Text>
              </div>
            </div>
          </Space>
        ),
      },

      {
        title: tWallet("points"),
        dataIndex: "point",
        key: "point",
        width: 120,
        render: (point: number) => (
          <Tag color={point > 500 ? "green" : point > 100 ? "orange" : "red"}>
            {point.toLocaleString()} {tWallet("points_unit")}
          </Tag>
        ),
        sorter: (a: Wallet, b: Wallet) => a.point - b.point,
      },
    ];

    return (
      <BaseTable<Wallet>
        ref={tableRef}
        api={api}
        columns={columns}
        rowKey="id"
        title={tTitle}
        showSearch={true}
        searchPlaceholder={tWallet("search_placeholder")}
        showActions={false}
      />
    );
  }
);

WalletTable.displayName = "WalletTable";

export default WalletTable;
