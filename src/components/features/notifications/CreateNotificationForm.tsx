// components/features/notifications/CreateNotificationForm.tsx
"use client";

import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseDatePicker from "@/components/ui/datepickers/BaseDatePicker";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import {
  NOTIFICATION_CHANNELS,
  NOTIFICATION_TYPES,
} from "@/constants/notifications";
import { UserRole } from "@/constants/userRole";
import { useNotification } from "@/contexts/NotiContext";
import NotificationService from "@/services/notificationService";
import UserService from "@/services/userService";
import {
  CreateNotificationRequest,
  NotificationType,
} from "@/types/notifications";
import { ClockCircleOutlined, SendOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Divider, Flex, Form, Typography } from "antd";
import dayjs from "dayjs";
import React, { useState } from "react";

const { Title, Text } = Typography;

interface CreateNotificationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const CreateNotificationForm: React.FC<CreateNotificationFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [sendType, setSendType] = useState<"now" | "scheduled">("now");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [targetAudience, setTargetAudience] = useState<
    "specific" | "all" | "role"
  >("specific");
  const notification = useNotification();

  // Fetch users for selection
  const fetchUsers = async (search: string) => {
    try {
      if (!search || search.length < 2) return [];

      const response = await UserService.getList({
        search,
        page: 1,
        pageSize: 20,
      });

      return response.data.map((user) => ({
        label: (
          <Flex justify="space-between" align="center">
            <span>{user.name}</span>
            <Text type="secondary" className="text-xs">
              {user.email}
            </Text>
          </Flex>
        ),
        value: user.id,
      }));
    } catch (error) {
      console.error("Failed to fetch users:", error);
      return [];
    }
  };
  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      let userIds: string[] = [];

      // Determine user IDs based on target audience
      if (targetAudience === "specific") {
        userIds = selectedUsers;
      } else if (targetAudience === "all") {
        // Get all users - this might need pagination handling
        const allUsers = await UserService.getList({ pageSize: 10000 });
        userIds = allUsers.data.map((user) => user.id.toString());
      } else if (targetAudience === "role") {
        // Get users by role
        const roleUsers = await UserService.getList({
          role: values.target_role,
          pageSize: 10000,
        });
        userIds = roleUsers.data.map((user) => user.id.toString());
      }

      if (userIds.length === 0) {
        notification.notifyError("Please select at least one recipient");
        return;
      }

      const requestData: CreateNotificationRequest = {
        user_ids: userIds,
        type: values.type,
        channel: values.channel,
        title: values.title,
        content: values.content,
        scheduled_at:
          sendType === "scheduled"
            ? values.scheduled_at?.toISOString()
            : undefined,
        metadata: {
          target_audience: targetAudience,
          target_role: values.target_role,
          created_by: UserRole.ADMIN, // This should come from auth context
        },
      };

      if (userIds.length === 1) {
        await NotificationService.create(requestData);
        notification.notifySuccess("Notification created successfully");
      } else {
        const result = await NotificationService.sendBulk(requestData);
        notification.notifySuccess(
          `Bulk notification sent to ${result.data.count} users`
        );
      }

      form.resetFields();
      onSuccess?.();
    } catch (error) {
      notification.notifyError("Failed to send notification");
      console.error("Send notification error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Template suggestions based on notification type
  const getTemplateSuggestion = (
    type: NotificationType
  ): { title: string; content: string } => {
    const templates = {
      welcome: {
        title: "Welcome to Flexi!",
        content:
          "Welcome to Flexi! We're excited to have you join our job platform. Start exploring opportunities today.",
      },
      verification: {
        title: "Verify Your Account",
        content:
          "Please verify your account by clicking the link we sent to your email address.",
      },
      password_reset: {
        title: "Password Reset Request",
        content:
          "We received a request to reset your password. Click the link below to create a new password.",
      },
      job_match: {
        title: "New Job Matches Available",
        content:
          "We found new job opportunities that match your profile. Check them out now!",
      },
      application_status: {
        title: "Application Status Update",
        content:
          "Your job application status has been updated. Please check your dashboard for details.",
      },
      interview_invitation: {
        title: "Interview Invitation",
        content:
          "Congratulations! You have been invited for an interview. Please check the details and confirm your availability.",
      },
      payment_confirmation: {
        title: "Payment Confirmation",
        content:
          "Your payment has been processed successfully. Thank you for your purchase.",
      },
      point_purchase: {
        title: "Points Purchase Confirmation",
        content: "Your points have been added to your account successfully.",
      },
      subscription_renewal: {
        title: "Subscription Renewal",
        content:
          "Your subscription has been renewed successfully. Thank you for continuing with us.",
      },
      system_maintenance: {
        title: "Scheduled Maintenance Notice",
        content:
          "We will be performing scheduled maintenance. The platform may be temporarily unavailable.",
      },
      policy_update: {
        title: "Policy Update Notice",
        content:
          "We have updated our terms and conditions. Please review the changes.",
      },
    };

    return templates[type] || { title: "", content: "" };
  };

  // Handle notification type change
  const handleTypeChange = (type: NotificationType) => {
    const template = getTemplateSuggestion(type);
    form.setFieldsValue({
      title: template.title,
      content: template.content,
    });
  };

  // Audience type options
  const audienceOptions = [
    { value: "specific", label: "Specific Users" },
    { value: "all", label: "All Users" },
    { value: "role", label: "By Role" },
  ];

  // Role options
  const roleOptions = [
    { value: UserRole.EMPLOYER, label: "Employers" },
    { value: UserRole.JOB_SEEKER, label: "Job Seekers" },
  ];

  return (
    <>
      <Typography.Title level={4}>Notification Management</Typography.Title>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="mt-4"
      >
        {/* Notification Type and Channel */}
        <Flex gap="large" wrap>
          <div className="flex-1 min-w-64">
            <BaseSelect
              label="Notification Type"
              name="type"
              required
              options={NOTIFICATION_TYPES}
              placeholder="Select notification type"
              onChange={handleTypeChange}
            />
          </div>
          <div className="flex-1 min-w-64">
            <BaseSelect
              label="Channel"
              name="channel"
              required
              options={NOTIFICATION_CHANNELS}
              placeholder="Select delivery channel"
            />
          </div>
        </Flex>

        <Divider />

        {/* Target Audience */}
        <Title level={5}>Target Audience</Title>
        <BaseSelect
          label="Audience Type"
          name="audience_type"
          required
          options={audienceOptions}
          placeholder="Select audience type"
          onChange={setTargetAudience}
          value={targetAudience}
        />

        {targetAudience === "specific" && (
          <DebounceSelect
            label="Select Users"
            name="users"
            fetchOptions={fetchUsers}
            placeholder="Search and select users..."
            mode="multiple"
            required
            helpText="Type at least 2 characters to search for users"
          />
        )}

        {targetAudience === "role" && (
          <BaseSelect
            label="Target Role"
            name="target_role"
            required
            options={roleOptions}
            placeholder="Select user role"
          />
        )}

        {targetAudience === "all" && (
          <Alert
            message="Send to All Users"
            description="This notification will be sent to all registered users on the platform."
            type="warning"
            showIcon
            className="mb-4"
          />
        )}

        <Divider />

        {/* Message Content */}
        <Title level={5}>Message Content</Title>
        <BaseInput
          label="Title"
          name="title"
          required
          placeholder="Enter notification title"
          maxLength={100}
          showCount
        />

        <BaseTextArea
          label="Content"
          name="content"
          required
          placeholder="Enter notification content"
          rows={6}
          maxLength={1000}
          showCount
          helpText="You can use variables like {{user_name}}, {{platform_name}}, etc."
        />

        <Divider />

        {/* Delivery Options */}
        <Title level={5}>Delivery Options</Title>
        <BaseSelect
          label="Send Time"
          name="send_time"
          required
          options={[
            { value: "now", label: "Send Now" },
            { value: "scheduled", label: "Schedule for Later" },
          ]}
          placeholder="Select when to send"
          onChange={setSendType}
          value={sendType}
        />

        {sendType === "scheduled" && (
          <BaseDatePicker
            label="Scheduled Time"
            name="scheduled_at"
            required
            placeholder="Select date and time"
            disabledDate={(current) => current && current < dayjs()}
          />
        )}

        {/* Actions */}
        <Flex gap="middle" justify="end" className="mt-6">
          <BaseButton label="Cancel" onClick={onCancel} />
          <BaseButton
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={
              sendType === "now" ? <SendOutlined /> : <ClockCircleOutlined />
            }
            label={sendType === "now" ? "Send Now" : "Schedule Notification"}
          />
        </Flex>
      </Form>
    </>
  );
};

export default CreateNotificationForm;
