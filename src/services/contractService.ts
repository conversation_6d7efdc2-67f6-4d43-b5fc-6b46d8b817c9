// services/ContractService.ts
import { mockContracts } from "@/mocks/contract";
// import ApiService from "@/services/ApiService";
import {
  Contract,
  TimeEntry,
  PaymentRecord,
  ContractMessage,
} from "@/types/contract";

// Helper function to delay responses to simulate API calls
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const ContractService = {
  async getContracts(params?: Record<string, unknown>) {
    await delay(500); // Simulate network delay

    let filteredContracts = [...mockContracts];

    // Filter by search
    if (params?.search) {
      const searchTerm = String(params.search).toLowerCase();
      filteredContracts = filteredContracts.filter(
        (contract) =>
          contract.title.toLowerCase().includes(searchTerm) ||
          contract.id.toLowerCase().includes(searchTerm)
      );
    }

    // Filter by status
    if (params?.status) {
      filteredContracts = filteredContracts.filter(
        (contract) => contract.status === params.status
      );
    }

    // Filter by contract type
    if (params?.contractType) {
      filteredContracts = filteredContracts.filter(
        (contract) => contract.contractType === params.contractType
      );
    }

    // Filter by employer
    if (params?.employerId) {
      filteredContracts = filteredContracts.filter(
        (contract) => contract.employerId === params.employerId
      );
    }

    // Filter by job seeker
    if (params?.jobSeekerId) {
      filteredContracts = filteredContracts.filter(
        (contract) => contract.jobSeekerId === params.jobSeekerId
      );
    }

    // Pagination
    const page = params?.page ? Number(params.page) : 1;
    const pageSize = params?.pageSize ? Number(params.pageSize) : 10;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedContracts = filteredContracts.slice(start, end);

    return {
      data: paginatedContracts,
      total: filteredContracts.length,
    };
  },

  async getContract(id: string) {
    await delay(300);
    const contract = mockContracts.find((c) => c.id === id);

    if (!contract) {
      throw new Error("Contract not found");
    }

    return contract;
  },

  async createContract() {
    console.log("Create contract");
  },

  async updateContract() {
    console.log("Update contract");
  },

  async deleteContract(id: string) {
    await delay(400);
    const index = mockContracts.findIndex((c) => c.id === id);

    if (index === -1) {
      throw new Error("Contract not found");
    }

    mockContracts.splice(index, 1);

    return { success: true };
  },

  async changeContractStatus(
    id: string,
    status: Contract["status"],
    reason?: string
  ) {
    await delay(300);
    const contract = mockContracts.find((c) => c.id === id);

    if (!contract) {
      throw new Error("Contract not found");
    }

    const now = new Date();

    if (status === "active" && contract.status === "offered") {
      contract.activatedAt = now;
    } else if (status === "terminated") {
      contract.terminatedAt = now;
      contract.terminationReason = reason || "";
    }

    contract.status = status;

    return contract;
  },

  async addTimeEntry(contractId: string, data: Partial<TimeEntry>) {
    await delay(400);
    const contract = mockContracts.find((c) => c.id === contractId);

    if (!contract) {
      throw new Error("Contract not found");
    }

    const newTimeEntry: TimeEntry = {
      id: `TE-${String(contract.timeEntries.length + 1).padStart(3, "0")}`,
      contractId: contractId,
      date: data.date ? new Date(data.date) : new Date(),
      hoursWorked: data.hoursWorked || 0,
      description: data.description || "",
      status: data.status || "pending",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    contract.timeEntries.push(newTimeEntry);

    return newTimeEntry;
  },

  async getTimeEntries(contractId: string, params?: Record<string, unknown>) {
    console.log("🚀 ~ getTimeEntries ~ params:", params);
    await delay(300);
    const contract = mockContracts.find((c) => c.id === contractId);

    if (!contract) {
      throw new Error("Contract not found");
    }

    return {
      data: contract.timeEntries,
      total: contract.timeEntries.length,
    };
  },

  async addPayment(contractId: string, data: Partial<PaymentRecord>) {
    await delay(500);
    const contract = mockContracts.find((c) => c.id === contractId);

    if (!contract) {
      throw new Error("Contract not found");
    }

    const newPayment: PaymentRecord = {
      id: `PAY-${String(contract.payments.length + 1).padStart(3, "0")}`,
      contractId: contractId,
      amount: data.amount || 0,
      date: data.date ? new Date(data.date) : new Date(),
      status: data.status || "pending",
      description: data.description || "",
      paymentMethod: data.paymentMethod || "bank_transfer",
      transactionId: data.transactionId,
      createdAt: new Date(),
    };

    contract.payments.push(newPayment);

    return newPayment;
  },

  async getPayments(params?: Record<string, unknown>) {
    console.log("🚀 ~ getPayments ~ params:", params);
    await delay(300);

    return {
      data: mockContracts[0].payments,
      total: mockContracts[0].payments.length,
    };
  },

  async sendMessage(contractId: string, message: string) {
    await delay(300);
    const contract = mockContracts.find((c) => c.id === contractId);

    if (!contract) {
      throw new Error("Contract not found");
    }

    // In a real app, we'd get sender info from auth
    const mockSenderId = "JS-001";
    const mockSenderName = "John Doe";

    const newMessage: ContractMessage = {
      id: `MSG-${String(contract.messages.length + 1).padStart(3, "0")}`,
      contractId: contractId,
      senderId: mockSenderId,
      senderName: mockSenderName,
      message: message,
      createdAt: new Date(),
      isRead: false,
    };

    contract.messages.push(newMessage);

    return newMessage;
  },

  async getMessages(contractId: string, params?: Record<string, unknown>) {
    console.log("🚀 ~ getMessages ~ params:", params);
    await delay(300);
    const contract = mockContracts.find((c) => c.id === contractId);

    if (!contract) {
      throw new Error("Contract not found");
    }

    return {
      data: contract.messages,
      total: contract.messages.length,
    };
  },
};

export default ContractService;
