import ApiService from "./ApiService";
import { WalletListResponse } from "@/types/wallet";
import UserService from "./userService";
import { UserRole } from "@/constants/userRole";

class WalletService {
  // Temporary implementation using user service until wallet API is available
  static async getJobSeekerWallets(
    params?: Record<string, unknown>
  ): Promise<WalletListResponse> {
    try {
      // Filter by jobSeeker role
      const response = await UserService.getList({
        ...params,
        role: UserRole.JOB_SEEKER,
      });

      // Transform user data to wallet format
      const walletData = response.data.map((user: any) => ({
        id: user.id,
        user: {
          id: user.id,
          ulid: user.ulid || "temp-ulid",
          name: user.name,
          email: user.email,
          phoneNumber: user.phoneNumber || "",
          profilePicture: user.profilePicture || "",
          address: user.address || {
            id: 0,
            detailAddress: "",
            ward: {
              code: 0,
              name: "",
              codename: "",
              divisionType: "",
              districtCode: 0,
            },
            district: {
              code: 0,
              name: "",
              codename: "",
              divisionType: "",
              provinceCode: 0,
              wards: [],
            },
            province: {
              code: 0,
              name: "",
              codename: "",
              divisionType: "",
              phoneCode: 0,
              districts: [],
            },
          },
        },
        point: Math.floor(Math.random() * 1000), // Random points for demo
      }));

      return {
        data: walletData,
        message: "Success",
        statusCode: 200,
        success: true,
      };
    } catch (error) {
      throw error;
    }
  }

  static async getEmployerWallets(
    params?: Record<string, unknown>
  ): Promise<WalletListResponse> {
    try {
      // Filter by employer role
      const response = await UserService.getList({
        ...params,
        role: UserRole.EMPLOYER,
      });

      // Transform user data to wallet format
      const walletData = response.data.map((user: any) => ({
        id: user.id,
        user: {
          id: user.id,
          ulid: user.ulid || "temp-ulid",
          name: user.name,
          email: user.email,
          phoneNumber: user.phoneNumber || "",
          profilePicture: user.profilePicture || "",
          address: user.address || {
            id: 0,
            detailAddress: "",
            ward: {
              code: 0,
              name: "",
              codename: "",
              divisionType: "",
              districtCode: 0,
            },
            district: {
              code: 0,
              name: "",
              codename: "",
              divisionType: "",
              provinceCode: 0,
              wards: [],
            },
            province: {
              code: 0,
              name: "",
              codename: "",
              divisionType: "",
              phoneCode: 0,
              districts: [],
            },
          },
        },
        point: Math.floor(Math.random() * 2000), // Random points for demo
      }));

      return {
        data: walletData,
        message: "Success",
        statusCode: 200,
        success: true,
      };
    } catch (error) {
      throw error;
    }
  }
}

export default WalletService;
