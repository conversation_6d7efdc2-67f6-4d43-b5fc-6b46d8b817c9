// services/interview/interviewService.ts
import {
  getMockInterview,
  getMockInterviews,
  mockInterviews,
  mockInterviewStats,
} from "@/mocks/interview";
import {
  Interview,
  InterviewFeedback,
  InterviewFormData,
  InterviewStatus,
} from "@/types/interview";

/**
 * Service for handling interview-related API requests with mock data
 */
const InterviewService = {
  /**
   * Fetch interviews with optional filtering
   */
  fetchInterviews: async (params?: Record<string, unknown>) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Use the getMockInterviews helper to filter and paginate
    return getMockInterviews(params);
  },

  /**
   * Fetch a single interview by ID
   */
  fetchInterviewById: async (id: string) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 300));

    const interview = getMockInterview(id);

    if (!interview) {
      throw new Error(`Interview with ID ${id} not found`);
    }

    return interview;
  },

  /**
   * Create a new interview
   */
  createInterview: async (interviewData: InterviewFormData) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 800));

    const now = new Date().toISOString();

    const newInterview: Interview = {
      id: `INT-${1000 + mockInterviews.length + 1}`,
      ...interviewData,
      jobPostTitle: "New Job Post", // In a real app, we'd get this from the job post
      candidateName: "New Candidate", // In a real app, we'd get this from the candidate
      employerName: "New Employer", // In a real app, we'd get this from the employer
      photoUrl: "https://randomuser.me/api/portraits/lego/1.jpg",
      feedbackProvided: false,
      createdAt: now,
      updatedAt: now,
    };

    mockInterviews.push(newInterview);

    return newInterview;
  },

  /**
   * Update an existing interview
   */
  updateInterview: async (
    id: string,
    interviewData: Partial<InterviewFormData>
  ) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 600));

    const index = mockInterviews.findIndex((interview) => interview.id === id);

    if (index === -1) {
      throw new Error(`Interview with ID ${id} not found`);
    }

    const updatedInterview = {
      ...mockInterviews[index],
      ...interviewData,
      updatedAt: new Date().toISOString(),
    };

    mockInterviews[index] = updatedInterview as Interview;

    return updatedInterview;
  },

  /**
   * Delete an interview
   */
  deleteInterview: async (id: string) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    const index = mockInterviews.findIndex((interview) => interview.id === id);

    if (index === -1) {
      throw new Error(`Interview with ID ${id} not found`);
    }

    mockInterviews.splice(index, 1);

    return true;
  },

  /**
   * Change interview status
   */
  changeInterviewStatus: async (id: string, status: InterviewStatus) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 400));

    const index = mockInterviews.findIndex((interview) => interview.id === id);

    if (index === -1) {
      throw new Error(`Interview with ID ${id} not found`);
    }

    const updatedInterview = {
      ...mockInterviews[index],
      status,
      updatedAt: new Date().toISOString(),
    };

    mockInterviews[index] = updatedInterview;

    return updatedInterview;
  },

  /**
   * Submit feedback for an interview
   */
  submitFeedback: async (id: string, feedback: InterviewFeedback) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 700));

    const index = mockInterviews.findIndex((interview) => interview.id === id);

    if (index === -1) {
      throw new Error(`Interview with ID ${id} not found`);
    }

    const updatedInterview = {
      ...mockInterviews[index],
      feedback,
      feedbackProvided: true,
      updatedAt: new Date().toISOString(),
    };

    mockInterviews[index] = updatedInterview;

    return updatedInterview;
  },

  /**
   * Get interview statistics
   */
  getInterviewStats: async (params?: Record<string, unknown>) => {
    console.log("🚀 ~ getInterviewStats: ~ params:", params);
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 600));

    // In a real app, we'd filter stats based on params
    return mockInterviewStats;
  },

  /**
   * Send notification about interview
   */
  sendNotification: async (
    id: string,
    notificationData: {
      recipient: "candidate" | "employer" | "both";
      method: "email" | "sms" | "app";
      template: string;
      message?: string;
    }
  ) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 800));

    const interview = mockInterviews.find((interview) => interview.id === id);

    if (!interview) {
      throw new Error(`Interview with ID ${id} not found`);
    }

    // In a real app, we would send notifications through the appropriate channels
    console.log(
      `Notification sent to ${notificationData.recipient} via ${notificationData.method}`,
      {
        interview,
        notificationData,
      }
    );

    return true;
  },
};

export default InterviewService;
