// AUTO-GENERATED FILE. DO NOT EDIT.
export enum PermissionEnum {
  VIEW_ACCOUNT_MANAGEMENT = "view_account_management",
  RESUME_DELETE = "resume_delete",
  RESUME_UPDATE = "resume_update",
  RESUME_CREATE = "resume_create",
  RESUME_READ = "resume_read",
  POST_APPLICATION_DELETE = "post_application_delete",
  POST_APPLICATION_UPDATE = "post_application_update",
  POST_APPLICATION_READ = "post_application_read",
  POST_STATISTICS = "post_statistics",
  POST_REJECT = "post_reject",
  POST_APPROVE = "post_approve",
  POST_DELETE = "post_delete",
  POST_UPDATE = "post_update",
  POST_CREATE = "post_create",
  POST_READ = "post_read",
  COMPANY_DELETE_BRANCH = "company_delete_branch",
  COMPANY_UPDATE_BRANCH = "company_update_branch",
  COMPANY_CREATE_BRANCH = "company_create_branch",
  COMPANY_VERIFY = "company_verify",
  COMPANY_REMOVE_STAFF = "company_remove_staff",
  COMPANY_UPDATE_STAFF = "company_update_staff",
  COMPANY_INVITE_STAFF = "company_invite_staff",
  COMPANY_CREATE = "company_create",
  COMPANY_DELETE = "company_delete",
  COMPANY_UPDATE = "company_update",
  COMPANY_READ = "company_read",
  ROLE_CREATE = "role_create",
  USER_REMOVE_ROLE = "user_remove_role",
  USER_ASSIGN_ROLE = "user_assign_role",
  USER_CREATE = "user_create",
  USER_DELETE = "user_delete",
  USER_UPDATE = "user_update",
  USER_READ = "user_read",
  ROLE_UNASSIGN_PERMISSION = "role_unassign_permission",
  ROLE_ASSIGN_PERMISSION = "role_assign_permission",
  ROLE_UPDATE = "role_update",
  ROLE_DELETE = "role_delete",
  PERMISSION_READ = "permission_read",
  ROLE_WRITE = "role_write",
  ROLE_READ = "role_read",
}
